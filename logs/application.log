2025-06-29 00:33:13.989 [34mINFO [0;39m [main] com.example.pure.PureApplication : Starting PureApplication using Java 11.0.27 on DESKTOP-DQ33ANO with PID 12836 (C:\Users\<USER>\IdeaProjects\pure\target\classes started by Hao in C:\Users\<USER>\IdeaProjects\pure)
2025-06-29 00:33:13.989 [34mINFO [0;39m [background-preinit] o.h.validator.internal.util.Version : HV000001: Hibernate Validator 6.2.5.Final
2025-06-29 00:33:13.992 [39mDEBUG[0;39m [main] com.example.pure.PureApplication : Running with Spring Boot v2.7.18, Spring v5.3.31
2025-06-29 00:33:13.993 [34mINFO [0;39m [main] com.example.pure.PureApplication : The following 1 profile is active: "dev"
2025-06-29 00:33:14.916 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-29 00:33:14.917 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-29 00:33:14.949 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 22 ms. Found 0 Redis repository interfaces.
2025-06-29 00:33:15.041 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\AccessLogMapper.class]
2025-06-29 00:33:15.041 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\MessagesMapper.class]
2025-06-29 00:33:15.041 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OAuth2Mapper.class]
2025-06-29 00:33:15.041 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OperatingLogMapper.class]
2025-06-29 00:33:15.041 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoEpisodesMapper.class]
2025-06-29 00:33:15.041 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInfoTypeLinkMapper.class]
2025-06-29 00:33:15.041 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInteractionMapper.class]
2025-06-29 00:33:15.041 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoTypeMapper.class]
2025-06-29 00:33:15.041 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoUrlMapper.class]
2025-06-29 00:33:15.041 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\RoleMapper.class]
2025-06-29 00:33:15.041 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserMapper.class]
2025-06-29 00:33:15.041 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserProfileMapper.class]
2025-06-29 00:33:15.041 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserRoleMapper.class]
2025-06-29 00:33:15.041 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\VideoCommentInteractionMapper.class]
2025-06-29 00:33:15.042 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-06-29 00:33:15.043 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-06-29 00:33:15.043 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'messagesMapper' and 'com.example.pure.mapper.primary.MessagesMapper' mapperInterface
2025-06-29 00:33:15.044 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'messagesMapper'.
2025-06-29 00:33:15.044 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'OAuth2Mapper' and 'com.example.pure.mapper.primary.OAuth2Mapper' mapperInterface
2025-06-29 00:33:15.044 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'OAuth2Mapper'.
2025-06-29 00:33:15.044 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'operatingLogMapper' and 'com.example.pure.mapper.primary.OperatingLogMapper' mapperInterface
2025-06-29 00:33:15.044 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'operatingLogMapper'.
2025-06-29 00:33:15.044 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoEpisodesMapper' and 'com.example.pure.mapper.primary.PureVideoEpisodesMapper' mapperInterface
2025-06-29 00:33:15.045 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoEpisodesMapper'.
2025-06-29 00:33:15.045 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper' and 'com.example.pure.mapper.primary.PureVideoInfoTypeLinkMapper' mapperInterface
2025-06-29 00:33:15.045 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper'.
2025-06-29 00:33:15.045 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInteractionMapper' and 'com.example.pure.mapper.primary.PureVideoInteractionMapper' mapperInterface
2025-06-29 00:33:15.045 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInteractionMapper'.
2025-06-29 00:33:15.045 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoTypeMapper' and 'com.example.pure.mapper.primary.PureVideoTypeMapper' mapperInterface
2025-06-29 00:33:15.045 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoTypeMapper'.
2025-06-29 00:33:15.045 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoUrlMapper' and 'com.example.pure.mapper.primary.PureVideoUrlMapper' mapperInterface
2025-06-29 00:33:15.046 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoUrlMapper'.
2025-06-29 00:33:15.046 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-06-29 00:33:15.046 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-06-29 00:33:15.046 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-06-29 00:33:15.046 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-06-29 00:33:15.046 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userProfileMapper' and 'com.example.pure.mapper.primary.UserProfileMapper' mapperInterface
2025-06-29 00:33:15.046 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userProfileMapper'.
2025-06-29 00:33:15.046 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userRoleMapper' and 'com.example.pure.mapper.primary.UserRoleMapper' mapperInterface
2025-06-29 00:33:15.047 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userRoleMapper'.
2025-06-29 00:33:15.047 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'videoCommentInteractionMapper' and 'com.example.pure.mapper.primary.VideoCommentInteractionMapper' mapperInterface
2025-06-29 00:33:15.047 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'videoCommentInteractionMapper'.
2025-06-29 00:33:15.543 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat initialized with port(s): 8080 (http)
2025-06-29 00:33:15.550 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol : Initializing ProtocolHandler ["http-nio-8080"]
2025-06-29 00:33:15.551 [34mINFO [0;39m [main] o.a.catalina.core.StandardService : Starting service [Tomcat]
2025-06-29 00:33:15.551 [34mINFO [0;39m [main] o.a.catalina.core.StandardEngine : Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-06-29 00:33:15.678 [34mINFO [0;39m [main] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring embedded WebApplicationContext
2025-06-29 00:33:15.678 [34mINFO [0;39m [main] o.s.b.w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1636 ms
2025-06-29 00:33:15.997 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\AccessLogMapper.xml]'
2025-06-29 00:33:16.012 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\MessagesMapper.xml]'
2025-06-29 00:33:16.020 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OAuth2Mapper.xml]'
2025-06-29 00:33:16.030 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OperatingLogMapper.xml]'
2025-06-29 00:33:16.047 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoEpisodesMapper.xml]'
2025-06-29 00:33:16.054 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInfoTypeLinkMapper.xml]'
2025-06-29 00:33:16.064 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInteractionMapper.xml]'
2025-06-29 00:33:16.074 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoTypeMapper.xml]'
2025-06-29 00:33:16.085 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoUrlMapper.xml]'
2025-06-29 00:33:16.095 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\RoleMapper.xml]'
2025-06-29 00:33:16.111 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserMapper.xml]'
2025-06-29 00:33:16.119 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserProfileMapper.xml]'
2025-06-29 00:33:16.125 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserRoleMapper.xml]'
2025-06-29 00:33:16.138 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\VideoCommentInteractionMapper.xml]'
2025-06-29 00:33:16.161 [34mINFO [0;39m [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Starting...
2025-06-29 00:33:16.661 [34mINFO [0;39m [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Start completed.
2025-06-29 00:33:17.453 [39mDEBUG[0;39m [main] o.s.w.s.r.ResourceUrlEncodingFilter : Filter 'resourceUrlEncodingFilter' configured for use
2025-06-29 00:33:17.454 [39mDEBUG[0;39m [main] com.example.pure.filter.JwtFilter : Filter 'jwtFilter' configured for use
2025-06-29 00:33:17.757 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.PageRequestDTO)
2025-06-29 00:33:17.759 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.OperatingLogController; public com.example.pure.common.Result com.example.pure.controller.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.PageRequestDTO)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-29 00:33:18.356 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-06-29 00:33:18.356 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-06-29 00:33:18.358 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUsersByPage(com.example.pure.model.dto.PageRequestDTO)
2025-06-29 00:33:18.358 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUsersByPage(com.example.pure.model.dto.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-29 00:33:18.359 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getAllUsers()
2025-06-29 00:33:18.360 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-29 00:33:18.360 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#id)") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-06-29 00:33:18.362 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-06-29 00:33:18.362 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.deleteUser(java.lang.Long)
2025-06-29 00:33:18.363 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-29 00:33:18.363 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)
2025-06-29 00:33:18.363 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasRole('ADMIN') or isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-06-29 00:33:18.370 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)
2025-06-29 00:33:18.370 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())', filter: 'null', filterTarget: 'null']]
2025-06-29 00:33:18.371 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)
2025-06-29 00:33:18.371 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-29 00:33:18.501 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-06-29 00:33:18.503 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-06-29 00:33:18.507 [34mINFO [0;39m [main] o.s.s.web.DefaultSecurityFilterChain : Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@2f70d78c, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@456c53a, org.springframework.security.web.context.SecurityContextPersistenceFilter@5263f554, org.springframework.security.web.header.HeaderWriterFilter@7dbe858f, org.springframework.security.web.authentication.logout.LogoutFilter@5de6c7d7, com.example.pure.filter.JwtFilter@7ac90dab, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@95958d9, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@70fe33fa, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7916568d, org.springframework.security.web.session.SessionManagementFilter@285ac29, org.springframework.security.web.access.ExceptionTranslationFilter@643fed50, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6df4d8f1]
2025-06-29 00:33:18.509 [34mINFO [0;39m [main] com.example.pure.config.AsyncConfig : 创建文件操作异步任务线程池
2025-06-29 00:33:18.512 [34mINFO [0;39m [main] com.example.pure.config.AsyncConfig : 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-06-29 00:33:18.661 [39mDEBUG[0;39m [main] o.s.w.s.s.s.WebSocketHandlerMapping : Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-06-29 00:33:18.684 [34mINFO [0;39m [main] o.s.b.a.w.s.WelcomePageHandlerMapping : Adding welcome page: class path resource [static/index.html]
2025-06-29 00:33:18.752 [39mDEBUG[0;39m [main] o.s.w.s.m.m.a.RequestMappingHandlerMapping : 83 mappings in 'requestMappingHandlerMapping'
2025-06-29 00:33:18.761 [39mDEBUG[0;39m [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-06-29 00:33:19.144 [39mDEBUG[0;39m [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-06-29 00:33:19.255 [34mINFO [0;39m [main] o.s.b.a.e.web.EndpointLinksResolver : Exposing 16 endpoint(s) beneath base path '/actuator'
2025-06-29 00:33:19.278 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-06-29 00:33:19.278 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/password']
2025-06-29 00:33:19.278 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/createUser']
2025-06-29 00:33:19.278 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/refresh/**']
2025-06-29 00:33:19.278 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/captcha']
2025-06-29 00:33:19.279 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-06-29 00:33:19.279 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-06-29 00:33:19.279 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/**']
2025-06-29 00:33:19.279 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-06-29 00:33:19.279 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws-raw/**']
2025-06-29 00:33:19.279 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/render/**']
2025-06-29 00:33:19.279 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/callback/**']
2025-06-29 00:33:19.279 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/refresh/**']
2025-06-29 00:33:19.279 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/verification/**']
2025-06-29 00:33:19.279 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/update/password']
2025-06-29 00:33:19.279 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/videoUrl/**']
2025-06-29 00:33:19.279 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video-legacy/**']
2025-06-29 00:33:19.279 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image/**']
2025-06-29 00:33:19.279 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image-legacy/**']
2025-06-29 00:33:19.279 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download/**']
2025-06-29 00:33:19.279 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download-legacy/**']
2025-06-29 00:33:19.280 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-06-29 00:33:19.280 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-06-29 00:33:19.280 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-06-29 00:33:19.280 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-06-29 00:33:19.280 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-06-29 00:33:19.280 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-06-29 00:33:19.280 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-06-29 00:33:19.280 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-06-29 00:33:19.280 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-06-29 00:33:19.280 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-06-29 00:33:19.280 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-06-29 00:33:19.280 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-06-29 00:33:19.280 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/openapi.json']
2025-06-29 00:33:19.280 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v2/**']
2025-06-29 00:33:19.280 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-06-29 00:33:19.280 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/user/get/**']
2025-06-29 00:33:19.280 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/profile/**']
2025-06-29 00:33:19.281 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-06-29 00:33:19.281 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-06-29 00:33:19.281 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [authenticated] for any request
2025-06-29 00:33:19.282 [34mINFO [0;39m [main] o.s.s.web.DefaultSecurityFilterChain : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@18487f92, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@252f00ba, org.springframework.security.web.context.SecurityContextPersistenceFilter@36e9794c, org.springframework.security.web.header.HeaderWriterFilter@5cdf61d, org.springframework.web.filter.CorsFilter@4667c6d9, org.springframework.security.web.authentication.logout.LogoutFilter@60e5b3a8, com.example.pure.filter.JwtFilter@7ac90dab, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@78a0d664, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@1237aa73, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5efa05f9, org.springframework.security.web.session.SessionManagementFilter@4509414e, org.springframework.security.web.access.ExceptionTranslationFilter@4131f6db, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@3a744e32]
2025-06-29 00:33:19.326 [39mTRACE[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : Looking for @MessageExceptionHandler mappings: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@7b02e036, started on Sun Jun 29 00:33:14 CST 2025
2025-06-29 00:33:19.342 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.AuthController:
	
2025-06-29 00:33:19.342 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.DownloadController:
	
2025-06-29 00:33:19.342 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.FileManagerController:
	
2025-06-29 00:33:19.342 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.ImageController:
	
2025-06-29 00:33:19.342 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.MessagesController:
	
2025-06-29 00:33:19.342 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.OAuth2Controller:
	
2025-06-29 00:33:19.342 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.OperatingLogController:
	
2025-06-29 00:33:19.342 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureFileManagerController:
	
2025-06-29 00:33:19.342 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureImageFileController:
	
2025-06-29 00:33:19.343 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureVideoController:
	
2025-06-29 00:33:19.343 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureVideoInteractionController:
	
2025-06-29 00:33:19.343 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureVideoUrlController:
	
2025-06-29 00:33:19.343 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.QRCodeController:
	
2025-06-29 00:33:19.346 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.QRLoginController:
	{[MESSAGE],[/qrlogin/subscribe]}: subscribeQRStatus(String)
2025-06-29 00:33:19.347 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.R2Controller:
	
2025-06-29 00:33:19.347 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.UserController:
	
2025-06-29 00:33:19.347 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.UserProfileController:
	
2025-06-29 00:33:19.347 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.VerificationController:
	
2025-06-29 00:33:19.347 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.VideoCommentInteractionController:
	
2025-06-29 00:33:19.347 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.VideoController:
	
2025-06-29 00:33:19.347 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.WebSocketTestController:
	{[MESSAGE],[/message]}: broadcastMessage(Map)
	{[MESSAGE],[/echo]}: echo(String)
	{[MESSAGE],[/room/{roomId}]}: sendToRoom(String,Map)
	{[MESSAGE],[/chat]}: handleChatMessage(WebSocketMessage)
	{[MESSAGE],[/private-message]}: privateMessage(Map,SimpMessageHeaderAccessor,Principal)
2025-06-29 00:33:19.348 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-06-29 00:33:19.349 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-06-29 00:33:19.349 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-06-29 00:33:19.349 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerConfigResource:
	
2025-06-29 00:33:19.464 [39mDEBUG[0;39m [main] o.s.w.s.m.m.a.RequestMappingHandlerAdapter : ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-06-29 00:33:19.504 [39mDEBUG[0;39m [main] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-06-29 00:33:19.794 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol : Starting ProtocolHandler ["http-nio-8080"]
2025-06-29 00:33:19.802 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat started on port(s): 8080 (http) with context path ''
2025-06-29 00:33:19.804 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-06-29 00:33:19.804 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-06-29 00:33:19.804 [34mINFO [0;39m [main] o.s.m.s.b.SimpleBrokerMessageHandler : Starting...
2025-06-29 00:33:19.804 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@794b139b]
2025-06-29 00:33:19.804 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@794b139b]
2025-06-29 00:33:19.805 [34mINFO [0;39m [main] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@794b139b]]
2025-06-29 00:33:19.805 [34mINFO [0;39m [main] o.s.m.s.b.SimpleBrokerMessageHandler : Started.
2025-06-29 00:33:19.805 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-29 00:33:19.805 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-29 00:33:19.827 [34mINFO [0;39m [main] com.example.pure.PureApplication : Started PureApplication in 6.288 seconds (JVM running for 7.254)
2025-06-29 00:34:08.120 [34mINFO [0;39m [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-29 00:34:08.120 [34mINFO [0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Initializing Servlet 'dispatcherServlet'
2025-06-29 00:34:08.120 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected StandardServletMultipartResolver
2025-06-29 00:34:08.120 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected AcceptHeaderLocaleResolver
2025-06-29 00:34:08.120 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected FixedThemeResolver
2025-06-29 00:34:08.123 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@24a907f3
2025-06-29 00:34:08.124 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.support.SessionFlashMapManager@44e4e8e0
2025-06-29 00:34:08.124 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-06-29 00:34:08.124 [34mINFO [0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Completed initialization in 4 ms
2025-06-29 00:34:08.141 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Securing POST /api/videoUrl/video-episodes
2025-06-29 00:34:08.143 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-29 00:34:08.151 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#uploadVideo(VideoInfoWithEpisodesDto)
2025-06-29 00:34:08.154 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.a.AnonymousAuthenticationFilter : Set SecurityContextHolder to anonymous SecurityContext
2025-06-29 00:34:08.161 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /api/videoUrl/video-episodes] with attributes [permitAll]
2025-06-29 00:34:08.162 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Secured POST /api/videoUrl/video-episodes
2025-06-29 00:34:08.166 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : POST "/api/videoUrl/video-episodes", parameters={}
2025-06-29 00:34:08.168 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#uploadVideo(VideoInfoWithEpisodesDto)
2025-06-29 00:34:08.278 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [VideoInfoWithEpisodesDto(videoEpisodes=[VideoEpisodesDTO(id=null, videoInfoId=null, number=, objectK (truncated)...]
2025-06-29 00:34:08.865 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-29 00:34:08.869 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6ebaeeaf]
2025-06-29 00:34:08.876 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@648991604 wrapping com.mysql.cj.jdbc.ConnectionImpl@3aa78972] will be managed by Spring
2025-06-29 00:34:08.879 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.P.findVideoInfoByTitle : ==>  Preparing: SELECT id, title, cover_image_url, description, created_time, updated_time FROM video_info WHERE title = ? LIMIT 1
2025-06-29 00:34:08.902 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.P.findVideoInfoByTitle : ==> Parameters: 远井同学想度过青春！笨蛋与手机与浪漫(String)
2025-06-29 00:34:08.929 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.P.findVideoInfoByTitle : <==      Total: 0
2025-06-29 00:34:08.930 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6ebaeeaf]
2025-06-29 00:34:08.931 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6ebaeeaf]
2025-06-29 00:34:08.932 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6ebaeeaf]
2025-06-29 00:34:08.954 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : Using @ExceptionHandler com.example.pure.exception.GlobalExceptionHandler#handleBusinessException(BusinessException)
2025-06-29 00:34:08.955 [31mWARN [0;39m [http-nio-8080-exec-1] c.e.p.e.GlobalExceptionHandler : 业务异常: 查找不到视频信息
2025-06-29 00:34:08.974 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-29 00:34:08.982 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=600, message=查找不到视频信息, success=false, data=null, time=2025-06-28T16:34:08.955934800Z)]
2025-06-29 00:34:08.999 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : Resolved [com.example.pure.exception.BusinessException: 查找不到视频信息]
2025-06-29 00:34:08.999 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Completed 400 BAD_REQUEST
2025-06-29 00:34:09.000 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-29 00:34:09.065 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Securing POST /api/videoUrl/videoinfo-with-episodes
2025-06-29 00:34:09.066 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-29 00:34:09.066 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#uploadVideoWithEpisodes(VideoInfoWithEpisodesDto)
2025-06-29 00:34:09.067 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.s.w.a.AnonymousAuthenticationFilter : Set SecurityContextHolder to anonymous SecurityContext
2025-06-29 00:34:09.067 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /api/videoUrl/videoinfo-with-episodes] with attributes [permitAll]
2025-06-29 00:34:09.067 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Secured POST /api/videoUrl/videoinfo-with-episodes
2025-06-29 00:34:09.068 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : POST "/api/videoUrl/videoinfo-with-episodes", parameters={}
2025-06-29 00:34:09.068 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#uploadVideoWithEpisodes(VideoInfoWithEpisodesDto)
2025-06-29 00:34:09.069 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [VideoInfoWithEpisodesDto(videoEpisodes=[VideoEpisodesDTO(id=null, videoInfoId=null, number=, objectK (truncated)...]
2025-06-29 00:34:09.094 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-29 00:34:09.094 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@34d303dd]
2025-06-29 00:34:09.094 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1043032237 wrapping com.mysql.cj.jdbc.ConnectionImpl@3aa78972] will be managed by Spring
2025-06-29 00:34:09.094 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.P.findVideoInfoByTitle : ==>  Preparing: SELECT id, title, cover_image_url, description, created_time, updated_time FROM video_info WHERE title = ? LIMIT 1
2025-06-29 00:34:09.095 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.P.findVideoInfoByTitle : ==> Parameters: 远井同学想度过青春！笨蛋与手机与浪漫(String)
2025-06-29 00:34:09.110 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.P.findVideoInfoByTitle : <==      Total: 0
2025-06-29 00:34:09.110 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@34d303dd]
2025-06-29 00:34:09.111 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@34d303dd] from current transaction
2025-06-29 00:34:09.111 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.P.insertVideoInfo : ==>  Preparing: INSERT INTO video_info( title, cover_image_url, description, created_time, updated_time ) VALUES ( ?, ?, ?, NOW(), NOW() )
2025-06-29 00:34:09.113 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.P.insertVideoInfo : ==> Parameters: 远井同学想度过青春！笨蛋与手机与浪漫(String), https://lain.bgm.tv/r/400/pic/cover/l/49/5e/532690_DW89v.jpg(String), 改编自草莓王子成员 jel 担任原作的动画短片系列《远井同学》是一部从女高中生远井茜第一天上学时与英俊的同学 jel 交谈开始的校园喜剧。(String)
2025-06-29 00:34:09.136 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.P.insertVideoInfo : <==    Updates: 1
2025-06-29 00:34:09.140 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@34d303dd]
2025-06-29 00:34:09.140 [34mINFO [0;39m [http-nio-8080-exec-2] c.e.p.s.impl.PureVideoUrlServiceImpl : 插入视频信息成功：远井同学想度过青春！笨蛋与手机与浪漫
2025-06-29 00:34:09.142 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@34d303dd] from current transaction
2025-06-29 00:34:09.142 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.P.findVideoInfoByTitle : ==>  Preparing: SELECT id, title, cover_image_url, description, created_time, updated_time FROM video_info WHERE title = ? LIMIT 1
2025-06-29 00:34:09.143 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.P.findVideoInfoByTitle : ==> Parameters: 远井同学想度过青春！笨蛋与手机与浪漫(String)
2025-06-29 00:34:09.159 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.P.findVideoInfoByTitle : <==      Total: 1
2025-06-29 00:34:09.160 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@34d303dd]
2025-06-29 00:34:09.160 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@34d303dd] from current transaction
2025-06-29 00:34:09.160 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.P.findVideoTypeByName : ==>  Preparing: SELECT * FROM video_type WHERE name = ?
2025-06-29 00:34:09.161 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.P.findVideoTypeByName : ==> Parameters: 2025(String)
2025-06-29 00:34:09.172 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.P.findVideoTypeByName : <==      Total: 1
2025-06-29 00:34:09.172 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@34d303dd]
2025-06-29 00:34:09.173 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@34d303dd] from current transaction
2025-06-29 00:34:09.173 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.P.findVideoTypeByName : ==>  Preparing: SELECT * FROM video_type WHERE name = ?
2025-06-29 00:34:09.173 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.P.findVideoTypeByName : ==> Parameters: 日本(String)
2025-06-29 00:34:09.184 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.P.findVideoTypeByName : <==      Total: 1
2025-06-29 00:34:09.184 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@34d303dd]
2025-06-29 00:34:09.184 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@34d303dd] from current transaction
2025-06-29 00:34:09.184 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.P.findVideoTypeByName : ==>  Preparing: SELECT * FROM video_type WHERE name = ?
2025-06-29 00:34:09.185 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.P.findVideoTypeByName : ==> Parameters: 搞笑(String)
2025-06-29 00:34:09.196 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.P.findVideoTypeByName : <==      Total: 1
2025-06-29 00:34:09.197 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@34d303dd]
2025-06-29 00:34:09.197 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@34d303dd] from current transaction
2025-06-29 00:34:09.197 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.P.findVideoTypeByName : ==>  Preparing: SELECT * FROM video_type WHERE name = ?
2025-06-29 00:34:09.197 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.P.findVideoTypeByName : ==> Parameters: 校园(String)
2025-06-29 00:34:09.209 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.P.findVideoTypeByName : <==      Total: 1
2025-06-29 00:34:09.209 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@34d303dd]
2025-06-29 00:34:09.209 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@34d303dd] from current transaction
2025-06-29 00:34:09.209 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.P.findVideoTypeByName : ==>  Preparing: SELECT * FROM video_type WHERE name = ?
2025-06-29 00:34:09.209 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.P.findVideoTypeByName : ==> Parameters: 青春(String)
2025-06-29 00:34:09.221 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.P.findVideoTypeByName : <==      Total: 1
2025-06-29 00:34:09.221 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@34d303dd]
2025-06-29 00:34:09.222 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@34d303dd] from current transaction
2025-06-29 00:34:09.222 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.P.findVideoTypeByName : ==>  Preparing: SELECT * FROM video_type WHERE name = ?
2025-06-29 00:34:09.222 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.P.findVideoTypeByName : ==> Parameters: 日本动漫(String)
2025-06-29 00:34:09.234 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.P.findVideoTypeByName : <==      Total: 1
2025-06-29 00:34:09.234 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@34d303dd]
2025-06-29 00:34:09.236 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@34d303dd] from current transaction
2025-06-29 00:34:09.252 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.P.insertVideoInfoTypeLink : ==>  Preparing: INSERT INTO video_info_type_link( video_info_id, type_id, created_time, updated_time ) VALUES ( ?, ?, NOW(), NOW() ) , ( ?, ?, NOW(), NOW() ) , ( ?, ?, NOW(), NOW() ) , ( ?, ?, NOW(), NOW() ) , ( ?, ?, NOW(), NOW() ) , ( ?, ?, NOW(), NOW() )
2025-06-29 00:34:09.253 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.P.insertVideoInfoTypeLink : ==> Parameters: 77(Long), 36(Long), 77(Long), 30(Long), 77(Long), 48(Long), 77(Long), 42(Long), 77(Long), 38(Long), 77(Long), 35(Long)
2025-06-29 00:34:09.275 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.P.insertVideoInfoTypeLink : <==    Updates: 6
2025-06-29 00:34:09.277 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@34d303dd]
2025-06-29 00:34:09.277 [34mINFO [0;39m [http-nio-8080-exec-2] c.e.p.s.impl.PureVideoUrlServiceImpl : 插入视频类型关联信息成功，视频ID: 77
2025-06-29 00:34:09.281 [34mINFO [0;39m [file-async-1] c.e.p.s.impl.PureVideoUrlServiceImpl : 开始异步下载并上传视频文件 - videoId: 77, episode: , url: http://p16-va-tiktok.ibyteimg.com/obj/tos-alisg-v-0051c001-sg/ocIbBgQACuNJEDaeGDIGovLaTLgCeAeYIEGbmC
2025-06-29 00:34:09.281 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@34d303dd] from current transaction
2025-06-29 00:34:09.281 [34mINFO [0;39m [file-async-2] c.e.p.s.impl.PureVideoUrlServiceImpl : 开始异步生成雪碧图 - videoId: 77, episode: 
2025-06-29 00:34:09.282 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.P.insertVideoEpisodes : ==>  Preparing: INSERT INTO video_episodes( video_info_id, number, object_key, duration, sprite_sheet_object_key, created_time, updated_time ) VALUES ( ?, ?, ?, ?, ?, NOW(), NOW() )
2025-06-29 00:34:09.283 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.P.insertVideoEpisodes : ==> Parameters: 77(Long), (String), null, null, null
2025-06-29 00:34:09.305 [39mDEBUG[0;39m [http-nio-8080-exec-2] c.e.p.m.p.P.insertVideoEpisodes : <==    Updates: 1
2025-06-29 00:34:09.305 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@34d303dd]
2025-06-29 00:34:09.305 [34mINFO [0;39m [http-nio-8080-exec-2] c.e.p.s.impl.PureVideoUrlServiceImpl : 插入视频分集信息成功，共1集，视频ID: 77
2025-06-29 00:34:09.306 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@34d303dd]
2025-06-29 00:34:09.307 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@34d303dd]
2025-06-29 00:34:09.307 [39mDEBUG[0;39m [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@34d303dd]
2025-06-29 00:34:09.330 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : Using @ExceptionHandler com.example.pure.exception.GlobalExceptionHandler#handleException(Exception)
2025-06-29 00:34:09.332 [1;31mERROR[0;39m [http-nio-8080-exec-2] c.e.p.e.GlobalExceptionHandler : 服务器处理API出现异常
java.lang.IllegalArgumentException: Cache 'videoInfo' does not allow 'null' values. Avoid storing null via '@Cacheable(unless="#result == null")' or configure RedisCache to allow 'null' via RedisCacheConfiguration.
	at org.springframework.data.redis.cache.RedisCache.put(RedisCache.java:166)
	at org.springframework.cache.transaction.TransactionAwareCacheDecorator$1.afterCommit(TransactionAwareCacheDecorator.java:100)
	at org.springframework.transaction.support.TransactionSynchronizationUtils.invokeAfterCommit(TransactionSynchronizationUtils.java:135)
	at org.springframework.transaction.support.TransactionSynchronizationUtils.triggerAfterCommit(TransactionSynchronizationUtils.java:123)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.triggerAfterCommit(AbstractPlatformTransactionManager.java:936)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.processCommit(AbstractPlatformTransactionManager.java:782)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.commit(AbstractPlatformTransactionManager.java:711)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.commitTransactionAfterReturning(TransactionAspectSupport.java:654)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:407)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.service.impl.PureVideoUrlServiceImpl$$EnhancerBySpringCGLIB$$7754b21f.uploadVideoWithEpisodes(<generated>)
	at com.example.pure.controller.PureVideoUrlController.uploadVideoWithEpisodes(PureVideoUrlController.java:42)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-06-29 00:34:09.336 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-29 00:34:09.336 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Writing [Result(code=500, message=服务器处理API出现异常，请联系管理员, success=false, data=null, time=2025-06-28T16:34:09.335 (truncated)...]
2025-06-29 00:34:09.337 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : Resolved [java.lang.IllegalArgumentException: Cache 'videoInfo' does not allow 'null' values. Avoid storing null via '@Cacheable(unless="#result == null")' or configure RedisCache to allow 'null' via RedisCacheConfiguration.]
2025-06-29 00:34:09.338 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Completed 500 INTERNAL_SERVER_ERROR
2025-06-29 00:34:09.338 [39mDEBUG[0;39m [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-29 00:34:09.347 [34mINFO [0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl : Executing command: ffmpeg -i http://p16-va-tiktok.ibyteimg.com/obj/tos-alisg-v-0051c001-sg/ocIbBgQACuNJEDaeGDIGovLaTLgCeAeYIEGbmC -vf fps=1/3,scale=160:-1,tile=23x23 -q:v 2 -y d:\upload\video_sprites\77\output_.jpg
2025-06-29 00:34:09.358 [39mDEBUG[0;39m [file-async-1] o.s.w.r.f.client.ExchangeFunctions : [2c8b9702] HTTP GET http://p16-va-tiktok.ibyteimg.com/obj/tos-alisg-v-0051c001-sg/ocIbBgQACuNJEDaeGDIGovLaTLgCeAeYIEGbmC
2025-06-29 00:34:09.717 [39mDEBUG[0;39m [reactor-http-nio-2] o.s.w.r.f.client.ExchangeFunctions : [2c8b9702] [a0608cac-1] Response 200 OK
2025-06-29 00:34:10.196 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl : ffmpeg version 7.1.1-full_build-www.gyan.dev Copyright (c) 2000-2025 the FFmpeg developers
2025-06-29 00:34:10.196 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :   built with gcc 14.2.0 (Rev1, Built by MSYS2 project)
2025-06-29 00:34:10.196 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :   configuration: --enable-gpl --enable-version3 --enable-static --disable-w32threads --disable-autodetect --enable-fontconfig --enable-iconv --enable-gnutls --enable-lcms2 --enable-libxml2 --enable-gmp --enable-bzlib --enable-lzma --enable-libsnappy --enable-zlib --enable-librist --enable-libsrt --enable-libssh --enable-libzmq --enable-avisynth --enable-libbluray --enable-libcaca --enable-libdvdnav --enable-libdvdread --enable-sdl2 --enable-libaribb24 --enable-libaribcaption --enable-libdav1d --enable-libdavs2 --enable-libopenjpeg --enable-libquirc --enable-libuavs3d --enable-libxevd --enable-libzvbi --enable-libqrencode --enable-librav1e --enable-libsvtav1 --enable-libvvenc --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxavs2 --enable-libxeve --enable-libxvid --enable-libaom --enable-libjxl --enable-libvpx --enable-mediafoundation --enable-libass --enable-frei0r --enable-libfreetype --enable-libfribidi --enable-libharfbuzz --enable-liblensfun --enable-libvidstab --enable-libvmaf --enable-libzimg --enable-amf --enable-cuda-llvm --enable-cuvid --enable-dxva2 --enable-d3d11va --enable-d3d12va --enable-ffnvcodec --enable-libvpl --enable-nvdec --enable-nvenc --enable-vaapi --enable-libshaderc --enable-vulkan --enable-libplacebo --enable-opencl --enable-libcdio --enable-libgme --enable-libmodplug --enable-libopenmpt --enable-libopencore-amrwb --enable-libmp3lame --enable-libshine --enable-libtheora --enable-libtwolame --enable-libvo-amrwbenc --enable-libcodec2 --enable-libilbc --enable-libgsm --enable-liblc3 --enable-libopencore-amrnb --enable-libopus --enable-libspeex --enable-libvorbis --enable-ladspa --enable-libbs2b --enable-libflite --enable-libmysofa --enable-librubberband --enable-libsoxr --enable-chromaprint
2025-06-29 00:34:10.196 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :   libavutil      59. 39.100 / 59. 39.100
2025-06-29 00:34:10.196 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :   libavcodec     61. 19.101 / 61. 19.101
2025-06-29 00:34:10.196 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :   libavformat    61.  7.100 / 61.  7.100
2025-06-29 00:34:10.196 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :   libavdevice    61.  3.100 / 61.  3.100
2025-06-29 00:34:10.196 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :   libavfilter    10.  4.100 / 10.  4.100
2025-06-29 00:34:10.196 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :   libswscale      8.  3.100 /  8.  3.100
2025-06-29 00:34:10.196 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :   libswresample   5.  3.100 /  5.  3.100
2025-06-29 00:34:10.196 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :   libpostproc    58.  3.100 / 58.  3.100
2025-06-29 00:34:10.585 [39mDEBUG[0;39m [reactor-http-nio-2] org.springframework.web.HttpLogging : [2c8b9702] [a0608cac-1] Read 5992210 bytes
2025-06-29 00:34:10.586 [34mINFO [0;39m [file-async-1] c.e.p.s.impl.PureVideoUrlServiceImpl : 视频文件下载完成 - videoId: 77, episode: , 文件大小: 5992210 bytes
2025-06-29 00:34:11.439 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl : Input #0, mov,mp4,m4a,3gp,3g2,mj2, from 'http://p16-va-tiktok.ibyteimg.com/obj/tos-alisg-v-0051c001-sg/ocIbBgQACuNJEDaeGDIGovLaTLgCeAeYIEGbmC':
2025-06-29 00:34:11.439 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :   Metadata:
2025-06-29 00:34:11.439 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :     major_brand     : isom
2025-06-29 00:34:11.439 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :     minor_version   : 512
2025-06-29 00:34:11.439 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :     compatible_brands: isomiso2avc1mp41
2025-06-29 00:34:11.439 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :     encoder         : Lavf57.83.100
2025-06-29 00:34:11.439 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :     description     : Packed by Bilibili XCoder v2.0.2
2025-06-29 00:34:11.439 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :   Duration: 00:00:34.37, start: 0.000000, bitrate: 1394 kb/s
2025-06-29 00:34:11.439 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :   Stream #0:0[0x1](und): Video: h264 (High) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 1920x1080, 1214 kb/s, 29.97 fps, 29.97 tbr, 16k tbn (default)
2025-06-29 00:34:11.439 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :       Metadata:
2025-06-29 00:34:11.439 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :         handler_name    : VideoHandler
2025-06-29 00:34:11.439 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :         vendor_id       : [0][0][0][0]
2025-06-29 00:34:11.439 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :   Stream #0:1[0x2](und): Audio: aac (LC) (mp4a / 0x6134706D), 48000 Hz, stereo, fltp, 170 kb/s (default)
2025-06-29 00:34:11.439 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :       Metadata:
2025-06-29 00:34:11.439 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :         handler_name    : SoundHandler
2025-06-29 00:34:11.439 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :         vendor_id       : [0][0][0][0]
2025-06-29 00:34:11.444 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl : Stream mapping:
2025-06-29 00:34:11.444 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :   Stream #0:0 -> #0:0 (h264 (native) -> mjpeg (native))
2025-06-29 00:34:11.444 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl : Press [q] to stop, [?] for help
2025-06-29 00:34:12.308 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl : Output #0, image2, to 'd:\upload\video_sprites\77\output_.jpg':
2025-06-29 00:34:12.309 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :   Metadata:
2025-06-29 00:34:12.309 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :     major_brand     : isom
2025-06-29 00:34:12.309 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :     minor_version   : 512
2025-06-29 00:34:12.309 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :     compatible_brands: isomiso2avc1mp41
2025-06-29 00:34:12.309 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :     description     : Packed by Bilibili XCoder v2.0.2
2025-06-29 00:34:12.309 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :     encoder         : Lavf61.7.100
2025-06-29 00:34:12.309 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :   Stream #0:0(und): Video: mjpeg, yuv420p(pc, bt709, progressive), 3680x2070, q=2-31, 200 kb/s, 0.0006 fps, 0.0006 tbn (default)
2025-06-29 00:34:12.309 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :       Metadata:
2025-06-29 00:34:12.309 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :         handler_name    : VideoHandler
2025-06-29 00:34:12.309 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :         vendor_id       : [0][0][0][0]
2025-06-29 00:34:12.309 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :         encoder         : Lavc61.19.101 mjpeg
2025-06-29 00:34:12.309 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :       Side data:
2025-06-29 00:34:12.309 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :         cpb: bitrate max/min/avg: 0/0/200000 buffer size: 0 vbv_delay: N/A
2025-06-29 00:34:12.328 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl : [image2 @ 000002a269b4ac80] The specified filename 'd:\upload\video_sprites\77\output_.jpg' does not contain an image sequence pattern or a pattern is invalid.
2025-06-29 00:34:12.328 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl : [image2 @ 000002a269b4ac80] Use a pattern such as %03d for an image sequence or use the -update option (with -frames:v 1 if needed) to write a single image.
2025-06-29 00:34:12.329 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl : [out#0/image2 @ 000002a26942ecc0] video:116KiB audio:0KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing overhead: unknown
2025-06-29 00:34:12.329 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl : frame=    1 fps=0.0 q=2.0 Lsize=N/A time=00:26:27.00 bitrate=N/A speed=1.79e+03x    
2025-06-29 00:34:12.350 [34mINFO [0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl : 命令成功执行。
2025-06-29 00:34:13.843 [39mDEBUG[0;39m [file-async-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-29 00:34:13.843 [39mDEBUG[0;39m [file-async-2] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@ca2fdb5] was not registered for synchronization because synchronization is not active
2025-06-29 00:34:13.860 [39mDEBUG[0;39m [file-async-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1344387161 wrapping com.mysql.cj.jdbc.ConnectionImpl@3aa78972] will not be managed by Spring
2025-06-29 00:34:13.860 [39mDEBUG[0;39m [file-async-2] c.e.p.m.p.P.updateSpriteSheetObjectKey : ==>  Preparing: UPDATE video_episodes SET sprite_sheet_object_key = ?, updated_time = NOW() WHERE video_info_id = ? AND number = ?
2025-06-29 00:34:13.861 [39mDEBUG[0;39m [file-async-2] c.e.p.m.p.P.updateSpriteSheetObjectKey : ==> Parameters: video/44f19819-3f8f-466c-a22f-2a052275617a/.jpg(String), 77(Long), (String)
2025-06-29 00:34:13.923 [39mDEBUG[0;39m [file-async-2] c.e.p.m.p.P.updateSpriteSheetObjectKey : <==    Updates: 1
2025-06-29 00:34:13.923 [39mDEBUG[0;39m [file-async-2] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@ca2fdb5]
2025-06-29 00:34:13.924 [34mINFO [0;39m [file-async-2] c.e.p.s.impl.PureVideoUrlServiceImpl : 异步生成雪碧图成功并已更新数据库 - videoId: 77, episode: , objectKey: video/44f19819-3f8f-466c-a22f-2a052275617a/.jpg
2025-06-29 00:34:14.819 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.security.web.FilterChainProxy : Securing POST /api/videoUrl/video-episodes
2025-06-29 00:34:14.819 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-29 00:34:14.820 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#uploadVideo(VideoInfoWithEpisodesDto)
2025-06-29 00:34:14.820 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.s.w.a.AnonymousAuthenticationFilter : Set SecurityContextHolder to anonymous SecurityContext
2025-06-29 00:34:14.820 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /api/videoUrl/video-episodes] with attributes [permitAll]
2025-06-29 00:34:14.820 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.security.web.FilterChainProxy : Secured POST /api/videoUrl/video-episodes
2025-06-29 00:34:14.820 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.web.servlet.DispatcherServlet : POST "/api/videoUrl/video-episodes", parameters={}
2025-06-29 00:34:14.821 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#uploadVideo(VideoInfoWithEpisodesDto)
2025-06-29 00:34:14.822 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [VideoInfoWithEpisodesDto(videoEpisodes=[VideoEpisodesDTO(id=null, videoInfoId=null, number=, objectK (truncated)...]
2025-06-29 00:34:14.844 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-29 00:34:14.844 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3847181d]
2025-06-29 00:34:14.844 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@851407725 wrapping com.mysql.cj.jdbc.ConnectionImpl@3aa78972] will be managed by Spring
2025-06-29 00:34:14.844 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.P.findVideoInfoByTitle : ==>  Preparing: SELECT id, title, cover_image_url, description, created_time, updated_time FROM video_info WHERE title = ? LIMIT 1
2025-06-29 00:34:14.845 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.P.findVideoInfoByTitle : ==> Parameters: 青春笨蛋少年不做圣诞服女郎的梦(String)
2025-06-29 00:34:14.856 [39mDEBUG[0;39m [http-nio-8080-exec-4] c.e.p.m.p.P.findVideoInfoByTitle : <==      Total: 0
2025-06-29 00:34:14.856 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3847181d]
2025-06-29 00:34:14.856 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3847181d]
2025-06-29 00:34:14.856 [39mDEBUG[0;39m [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3847181d]
2025-06-29 00:34:14.877 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : Using @ExceptionHandler com.example.pure.exception.GlobalExceptionHandler#handleBusinessException(BusinessException)
2025-06-29 00:34:14.878 [31mWARN [0;39m [http-nio-8080-exec-4] c.e.p.e.GlobalExceptionHandler : 业务异常: 查找不到视频信息
2025-06-29 00:34:14.878 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-29 00:34:14.878 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=600, message=查找不到视频信息, success=false, data=null, time=2025-06-28T16:34:14.878425700Z)]
2025-06-29 00:34:14.880 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : Resolved [com.example.pure.exception.BusinessException: 查找不到视频信息]
2025-06-29 00:34:14.880 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.web.servlet.DispatcherServlet : Completed 400 BAD_REQUEST
2025-06-29 00:34:14.880 [39mDEBUG[0;39m [http-nio-8080-exec-4] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-29 00:34:14.934 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.security.web.FilterChainProxy : Securing POST /api/videoUrl/videoinfo-with-episodes
2025-06-29 00:34:14.934 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-29 00:34:14.935 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#uploadVideoWithEpisodes(VideoInfoWithEpisodesDto)
2025-06-29 00:34:14.936 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.s.w.a.AnonymousAuthenticationFilter : Set SecurityContextHolder to anonymous SecurityContext
2025-06-29 00:34:14.936 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /api/videoUrl/videoinfo-with-episodes] with attributes [permitAll]
2025-06-29 00:34:14.936 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.security.web.FilterChainProxy : Secured POST /api/videoUrl/videoinfo-with-episodes
2025-06-29 00:34:14.936 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : POST "/api/videoUrl/videoinfo-with-episodes", parameters={}
2025-06-29 00:34:14.937 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#uploadVideoWithEpisodes(VideoInfoWithEpisodesDto)
2025-06-29 00:34:14.938 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [VideoInfoWithEpisodesDto(videoEpisodes=[VideoEpisodesDTO(id=null, videoInfoId=null, number=, objectK (truncated)...]
2025-06-29 00:34:14.951 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-29 00:34:14.951 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@14947463]
2025-06-29 00:34:14.952 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@510402323 wrapping com.mysql.cj.jdbc.ConnectionImpl@3aa78972] will be managed by Spring
2025-06-29 00:34:14.952 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.P.findVideoInfoByTitle : ==>  Preparing: SELECT id, title, cover_image_url, description, created_time, updated_time FROM video_info WHERE title = ? LIMIT 1
2025-06-29 00:34:14.953 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.P.findVideoInfoByTitle : ==> Parameters: 青春笨蛋少年不做圣诞服女郎的梦(String)
2025-06-29 00:34:14.963 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.P.findVideoInfoByTitle : <==      Total: 0
2025-06-29 00:34:14.964 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@14947463]
2025-06-29 00:34:14.964 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@14947463] from current transaction
2025-06-29 00:34:14.964 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.P.insertVideoInfo : ==>  Preparing: INSERT INTO video_info( title, cover_image_url, description, created_time, updated_time ) VALUES ( ?, ?, ?, NOW(), NOW() )
2025-06-29 00:34:14.965 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.P.insertVideoInfo : ==> Parameters: 青春笨蛋少年不做圣诞服女郎的梦(String), https://lain.bgm.tv/r/400/pic/cover/l/d3/a3/467930_jH4s4.jpg(String), 青春期症候群——传闻的奇怪现象造成不稳定的精神状态。在高中时代遇到了许多患有各种青春期症候群的少女的“梓川咲太”也成了大学生。国民人气女演员，恋人“樱岛麻衣”与他一起就读于金泽八景的大学，在校内发现了不合季节的迷你裙圣诞老人。吓了一跳。我看到了我自己。好像在哪里听过的台词。说青春期症候群是礼物的迷你裙圣诞老人告诉咲太。……我叫雾岛透子社交网络上流行的预知梦、身份不明的网络歌手、色情狂、伴随着谜一般的现象，心灵动摇的少女们之间不可思议的故事再次开始。青春期不会结束——新的舞台，新的人思春期幻想生动地描绘青春期特有的感情和扣人心弦的人情剧，随着作品的不断更新，“青春猪头少年”系列也获得了新的粉丝。高中少年成为大学生，在新的人际关系中面对新的烦恼和不可思议现象。(String)
2025-06-29 00:34:14.987 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.P.insertVideoInfo : <==    Updates: 1
2025-06-29 00:34:14.987 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@14947463]
2025-06-29 00:34:14.988 [34mINFO [0;39m [http-nio-8080-exec-3] c.e.p.s.impl.PureVideoUrlServiceImpl : 插入视频信息成功：青春笨蛋少年不做圣诞服女郎的梦
2025-06-29 00:34:14.991 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@14947463] from current transaction
2025-06-29 00:34:14.991 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.P.findVideoInfoByTitle : ==>  Preparing: SELECT id, title, cover_image_url, description, created_time, updated_time FROM video_info WHERE title = ? LIMIT 1
2025-06-29 00:34:14.991 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.P.findVideoInfoByTitle : ==> Parameters: 青春笨蛋少年不做圣诞服女郎的梦(String)
2025-06-29 00:34:15.003 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.P.findVideoInfoByTitle : <==      Total: 1
2025-06-29 00:34:15.004 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@14947463]
2025-06-29 00:34:15.004 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@14947463] from current transaction
2025-06-29 00:34:15.004 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.P.findVideoTypeByName : ==>  Preparing: SELECT * FROM video_type WHERE name = ?
2025-06-29 00:34:15.004 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.P.findVideoTypeByName : ==> Parameters: 2015(String)
2025-06-29 00:34:15.015 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.P.findVideoTypeByName : <==      Total: 1
2025-06-29 00:34:15.016 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@14947463]
2025-06-29 00:34:15.016 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@14947463] from current transaction
2025-06-29 00:34:15.016 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.P.findVideoTypeByName : ==>  Preparing: SELECT * FROM video_type WHERE name = ?
2025-06-29 00:34:15.016 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.P.findVideoTypeByName : ==> Parameters: 日本(String)
2025-06-29 00:34:15.026 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.P.findVideoTypeByName : <==      Total: 1
2025-06-29 00:34:15.027 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@14947463]
2025-06-29 00:34:15.027 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@14947463] from current transaction
2025-06-29 00:34:15.027 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.P.findVideoTypeByName : ==>  Preparing: SELECT * FROM video_type WHERE name = ?
2025-06-29 00:34:15.028 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.P.findVideoTypeByName : ==> Parameters: 校园(String)
2025-06-29 00:34:15.038 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.P.findVideoTypeByName : <==      Total: 1
2025-06-29 00:34:15.039 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@14947463]
2025-06-29 00:34:15.039 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@14947463] from current transaction
2025-06-29 00:34:15.039 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.P.findVideoTypeByName : ==>  Preparing: SELECT * FROM video_type WHERE name = ?
2025-06-29 00:34:15.039 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.P.findVideoTypeByName : ==> Parameters: 青春(String)
2025-06-29 00:34:15.051 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.P.findVideoTypeByName : <==      Total: 1
2025-06-29 00:34:15.052 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@14947463]
2025-06-29 00:34:15.052 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@14947463] from current transaction
2025-06-29 00:34:15.052 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.P.findVideoTypeByName : ==>  Preparing: SELECT * FROM video_type WHERE name = ?
2025-06-29 00:34:15.053 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.P.findVideoTypeByName : ==> Parameters: 恋爱(String)
2025-06-29 00:34:15.063 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.P.findVideoTypeByName : <==      Total: 1
2025-06-29 00:34:15.064 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@14947463]
2025-06-29 00:34:15.064 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@14947463] from current transaction
2025-06-29 00:34:15.064 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.P.findVideoTypeByName : ==>  Preparing: SELECT * FROM video_type WHERE name = ?
2025-06-29 00:34:15.064 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.P.findVideoTypeByName : ==> Parameters: 日本动漫(String)
2025-06-29 00:34:15.074 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.P.findVideoTypeByName : <==      Total: 1
2025-06-29 00:34:15.075 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@14947463]
2025-06-29 00:34:15.075 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@14947463] from current transaction
2025-06-29 00:34:15.075 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.P.insertVideoInfoTypeLink : ==>  Preparing: INSERT INTO video_info_type_link( video_info_id, type_id, created_time, updated_time ) VALUES ( ?, ?, NOW(), NOW() ) , ( ?, ?, NOW(), NOW() ) , ( ?, ?, NOW(), NOW() ) , ( ?, ?, NOW(), NOW() ) , ( ?, ?, NOW(), NOW() ) , ( ?, ?, NOW(), NOW() )
2025-06-29 00:34:15.077 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.P.insertVideoInfoTypeLink : ==> Parameters: 78(Long), 57(Long), 78(Long), 30(Long), 78(Long), 42(Long), 78(Long), 38(Long), 78(Long), 33(Long), 78(Long), 35(Long)
2025-06-29 00:34:15.096 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.P.insertVideoInfoTypeLink : <==    Updates: 6
2025-06-29 00:34:15.097 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@14947463]
2025-06-29 00:34:15.097 [34mINFO [0;39m [http-nio-8080-exec-3] c.e.p.s.impl.PureVideoUrlServiceImpl : 插入视频类型关联信息成功，视频ID: 78
2025-06-29 00:34:15.097 [34mINFO [0;39m [file-async-3] c.e.p.s.impl.PureVideoUrlServiceImpl : 开始异步下载并上传视频文件 - videoId: 78, episode: , url: http://p16-va-tiktok.ibyteimg.com/obj/tos-alisg-v-0051c001-sg/oEUDjKFXgIDg9hgAAongf8EKoeI0Nx6BQWDhEB
2025-06-29 00:34:15.098 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@14947463] from current transaction
2025-06-29 00:34:15.098 [34mINFO [0;39m [file-async-4] c.e.p.s.impl.PureVideoUrlServiceImpl : 开始异步生成雪碧图 - videoId: 78, episode: 
2025-06-29 00:34:15.098 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.P.insertVideoEpisodes : ==>  Preparing: INSERT INTO video_episodes( video_info_id, number, object_key, duration, sprite_sheet_object_key, created_time, updated_time ) VALUES ( ?, ?, ?, ?, ?, NOW(), NOW() )
2025-06-29 00:34:15.098 [39mDEBUG[0;39m [file-async-3] o.s.w.r.f.client.ExchangeFunctions : [6aaa0274] HTTP GET http://p16-va-tiktok.ibyteimg.com/obj/tos-alisg-v-0051c001-sg/oEUDjKFXgIDg9hgAAongf8EKoeI0Nx6BQWDhEB
2025-06-29 00:34:15.098 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.P.insertVideoEpisodes : ==> Parameters: 78(Long), (String), null, null, null
2025-06-29 00:34:15.120 [39mDEBUG[0;39m [http-nio-8080-exec-3] c.e.p.m.p.P.insertVideoEpisodes : <==    Updates: 1
2025-06-29 00:34:15.121 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@14947463]
2025-06-29 00:34:15.121 [34mINFO [0;39m [http-nio-8080-exec-3] c.e.p.s.impl.PureVideoUrlServiceImpl : 插入视频分集信息成功，共1集，视频ID: 78
2025-06-29 00:34:15.121 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@14947463]
2025-06-29 00:34:15.121 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@14947463]
2025-06-29 00:34:15.121 [39mDEBUG[0;39m [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@14947463]
2025-06-29 00:34:15.124 [34mINFO [0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl : Executing command: ffmpeg -i http://p16-va-tiktok.ibyteimg.com/obj/tos-alisg-v-0051c001-sg/oEUDjKFXgIDg9hgAAongf8EKoeI0Nx6BQWDhEB -vf fps=1/3,scale=160:-1,tile=23x23 -q:v 2 -y d:\upload\video_sprites\78\output_.jpg
2025-06-29 00:34:15.143 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : Using @ExceptionHandler com.example.pure.exception.GlobalExceptionHandler#handleException(Exception)
2025-06-29 00:34:15.143 [1;31mERROR[0;39m [http-nio-8080-exec-3] c.e.p.e.GlobalExceptionHandler : 服务器处理API出现异常
java.lang.IllegalArgumentException: Cache 'videoInfo' does not allow 'null' values. Avoid storing null via '@Cacheable(unless="#result == null")' or configure RedisCache to allow 'null' via RedisCacheConfiguration.
	at org.springframework.data.redis.cache.RedisCache.put(RedisCache.java:166)
	at org.springframework.cache.transaction.TransactionAwareCacheDecorator$1.afterCommit(TransactionAwareCacheDecorator.java:100)
	at org.springframework.transaction.support.TransactionSynchronizationUtils.invokeAfterCommit(TransactionSynchronizationUtils.java:135)
	at org.springframework.transaction.support.TransactionSynchronizationUtils.triggerAfterCommit(TransactionSynchronizationUtils.java:123)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.triggerAfterCommit(AbstractPlatformTransactionManager.java:936)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.processCommit(AbstractPlatformTransactionManager.java:782)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.commit(AbstractPlatformTransactionManager.java:711)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.commitTransactionAfterReturning(TransactionAspectSupport.java:654)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:407)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.service.impl.PureVideoUrlServiceImpl$$EnhancerBySpringCGLIB$$7754b21f.uploadVideoWithEpisodes(<generated>)
	at com.example.pure.controller.PureVideoUrlController.uploadVideoWithEpisodes(PureVideoUrlController.java:42)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-06-29 00:34:15.144 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-29 00:34:15.144 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Writing [Result(code=500, message=服务器处理API出现异常，请联系管理员, success=false, data=null, time=2025-06-28T16:34:15.144 (truncated)...]
2025-06-29 00:34:15.145 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : Resolved [java.lang.IllegalArgumentException: Cache 'videoInfo' does not allow 'null' values. Avoid storing null via '@Cacheable(unless="#result == null")' or configure RedisCache to allow 'null' via RedisCacheConfiguration.]
2025-06-29 00:34:15.146 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : Completed 500 INTERNAL_SERVER_ERROR
2025-06-29 00:34:15.146 [39mDEBUG[0;39m [http-nio-8080-exec-3] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-29 00:34:15.169 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl : ffmpeg version 7.1.1-full_build-www.gyan.dev Copyright (c) 2000-2025 the FFmpeg developers
2025-06-29 00:34:15.169 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :   built with gcc 14.2.0 (Rev1, Built by MSYS2 project)
2025-06-29 00:34:15.169 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :   configuration: --enable-gpl --enable-version3 --enable-static --disable-w32threads --disable-autodetect --enable-fontconfig --enable-iconv --enable-gnutls --enable-lcms2 --enable-libxml2 --enable-gmp --enable-bzlib --enable-lzma --enable-libsnappy --enable-zlib --enable-librist --enable-libsrt --enable-libssh --enable-libzmq --enable-avisynth --enable-libbluray --enable-libcaca --enable-libdvdnav --enable-libdvdread --enable-sdl2 --enable-libaribb24 --enable-libaribcaption --enable-libdav1d --enable-libdavs2 --enable-libopenjpeg --enable-libquirc --enable-libuavs3d --enable-libxevd --enable-libzvbi --enable-libqrencode --enable-librav1e --enable-libsvtav1 --enable-libvvenc --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxavs2 --enable-libxeve --enable-libxvid --enable-libaom --enable-libjxl --enable-libvpx --enable-mediafoundation --enable-libass --enable-frei0r --enable-libfreetype --enable-libfribidi --enable-libharfbuzz --enable-liblensfun --enable-libvidstab --enable-libvmaf --enable-libzimg --enable-amf --enable-cuda-llvm --enable-cuvid --enable-dxva2 --enable-d3d11va --enable-d3d12va --enable-ffnvcodec --enable-libvpl --enable-nvdec --enable-nvenc --enable-vaapi --enable-libshaderc --enable-vulkan --enable-libplacebo --enable-opencl --enable-libcdio --enable-libgme --enable-libmodplug --enable-libopenmpt --enable-libopencore-amrwb --enable-libmp3lame --enable-libshine --enable-libtheora --enable-libtwolame --enable-libvo-amrwbenc --enable-libcodec2 --enable-libilbc --enable-libgsm --enable-liblc3 --enable-libopencore-amrnb --enable-libopus --enable-libspeex --enable-libvorbis --enable-ladspa --enable-libbs2b --enable-libflite --enable-libmysofa --enable-librubberband --enable-libsoxr --enable-chromaprint
2025-06-29 00:34:15.169 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :   libavutil      59. 39.100 / 59. 39.100
2025-06-29 00:34:15.169 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :   libavcodec     61. 19.101 / 61. 19.101
2025-06-29 00:34:15.169 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :   libavformat    61.  7.100 / 61.  7.100
2025-06-29 00:34:15.169 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :   libavdevice    61.  3.100 / 61.  3.100
2025-06-29 00:34:15.169 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :   libavfilter    10.  4.100 / 10.  4.100
2025-06-29 00:34:15.169 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :   libswscale      8.  3.100 /  8.  3.100
2025-06-29 00:34:15.169 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :   libswresample   5.  3.100 /  5.  3.100
2025-06-29 00:34:15.169 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :   libpostproc    58.  3.100 / 58.  3.100
2025-06-29 00:34:15.266 [39mDEBUG[0;39m [reactor-http-nio-2] o.s.w.r.f.client.ExchangeFunctions : [6aaa0274] [a0608cac-2] Response 200 OK
2025-06-29 00:34:15.548 [34mINFO [0;39m [file-async-1] c.e.p.s.impl.PureVideoUrlServiceImpl : 视频文件上传到R2成功 - videoId: 77, episode: , objectKey: video/44f19819-3f8f-466c-a22f-2a052275617a/.mp4
2025-06-29 00:34:15.548 [39mDEBUG[0;39m [file-async-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-29 00:34:15.548 [39mDEBUG[0;39m [file-async-1] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@53d9c69d] was not registered for synchronization because synchronization is not active
2025-06-29 00:34:15.548 [39mDEBUG[0;39m [file-async-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1881969402 wrapping com.mysql.cj.jdbc.ConnectionImpl@3aa78972] will not be managed by Spring
2025-06-29 00:34:15.548 [39mDEBUG[0;39m [file-async-1] c.e.p.m.p.P.updateEpisodeObjectKey : ==>  Preparing: UPDATE video_episodes SET object_key = ?, updated_time = NOW() WHERE video_info_id = ? AND number = ?
2025-06-29 00:34:15.549 [39mDEBUG[0;39m [file-async-1] c.e.p.m.p.P.updateEpisodeObjectKey : ==> Parameters: video/44f19819-3f8f-466c-a22f-2a052275617a/.mp4(String), 77(Long), (String)
2025-06-29 00:34:15.571 [39mDEBUG[0;39m [file-async-1] c.e.p.m.p.P.updateEpisodeObjectKey : <==    Updates: 1
2025-06-29 00:34:15.571 [39mDEBUG[0;39m [file-async-1] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@53d9c69d]
2025-06-29 00:34:15.571 [34mINFO [0;39m [file-async-1] c.e.p.s.impl.PureVideoUrlServiceImpl : 异步更新数据库objectKey成功 - videoId: 77, episode: , objectKey: video/44f19819-3f8f-466c-a22f-2a052275617a/.mp4
2025-06-29 00:34:16.234 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl : Input #0, mov,mp4,m4a,3gp,3g2,mj2, from 'http://p16-va-tiktok.ibyteimg.com/obj/tos-alisg-v-0051c001-sg/oEUDjKFXgIDg9hgAAongf8EKoeI0Nx6BQWDhEB':
2025-06-29 00:34:16.234 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :   Metadata:
2025-06-29 00:34:16.234 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :     major_brand     : isom
2025-06-29 00:34:16.234 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :     minor_version   : 512
2025-06-29 00:34:16.234 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :     compatible_brands: isomiso2avc1mp41
2025-06-29 00:34:16.234 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :     encoder         : Lavf57.83.100
2025-06-29 00:34:16.234 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :     description     : Packed by Bilibili XCoder v2.0.2
2025-06-29 00:34:16.234 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :   Duration: 00:01:43.14, start: 0.000000, bitrate: 919 kb/s
2025-06-29 00:34:16.234 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :   Stream #0:0[0x1](und): Video: h264 (High) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 1920x1080 [SAR 1:1 DAR 16:9], 796 kb/s, 30 fps, 30 tbr, 16k tbn (default)
2025-06-29 00:34:16.234 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :       Metadata:
2025-06-29 00:34:16.234 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :         handler_name    : VideoHandler
2025-06-29 00:34:16.234 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :         vendor_id       : [0][0][0][0]
2025-06-29 00:34:16.235 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :   Stream #0:1[0x2](und): Audio: aac (LC) (mp4a / 0x6134706D), 44100 Hz, stereo, fltp, 112 kb/s (default)
2025-06-29 00:34:16.235 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :       Metadata:
2025-06-29 00:34:16.235 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :         handler_name    : SoundHandler
2025-06-29 00:34:16.235 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :         vendor_id       : [0][0][0][0]
2025-06-29 00:34:16.239 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl : Stream mapping:
2025-06-29 00:34:16.239 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :   Stream #0:0 -> #0:0 (h264 (native) -> mjpeg (native))
2025-06-29 00:34:16.239 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl : Press [q] to stop, [?] for help
2025-06-29 00:34:16.715 [39mDEBUG[0;39m [reactor-http-nio-2] org.springframework.web.HttpLogging : [6aaa0274] [a0608cac-2] Read 11855017 bytes
2025-06-29 00:34:16.715 [34mINFO [0;39m [file-async-3] c.e.p.s.impl.PureVideoUrlServiceImpl : 视频文件下载完成 - videoId: 78, episode: , 文件大小: 11855017 bytes
2025-06-29 00:34:19.232 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl : Output #0, image2, to 'd:\upload\video_sprites\78\output_.jpg':
2025-06-29 00:34:19.232 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :   Metadata:
2025-06-29 00:34:19.232 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :     major_brand     : isom
2025-06-29 00:34:19.232 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :     minor_version   : 512
2025-06-29 00:34:19.232 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :     compatible_brands: isomiso2avc1mp41
2025-06-29 00:34:19.232 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :     description     : Packed by Bilibili XCoder v2.0.2
2025-06-29 00:34:19.233 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :     encoder         : Lavf61.7.100
2025-06-29 00:34:19.233 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :   Stream #0:0(und): Video: mjpeg, yuv420p(pc, bt709, progressive), 3680x2070 [SAR 1:1 DAR 16:9], q=2-31, 200 kb/s, 0.0006 fps, 0.0006 tbn (default)
2025-06-29 00:34:19.233 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :       Metadata:
2025-06-29 00:34:19.233 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :         handler_name    : VideoHandler
2025-06-29 00:34:19.233 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :         vendor_id       : [0][0][0][0]
2025-06-29 00:34:19.233 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :         encoder         : Lavc61.19.101 mjpeg
2025-06-29 00:34:19.233 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :       Side data:
2025-06-29 00:34:19.233 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :         cpb: bitrate max/min/avg: 0/0/200000 buffer size: 0 vbv_delay: N/A
2025-06-29 00:34:19.253 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl : [image2 @ 0000020ac4665c00] The specified filename 'd:\upload\video_sprites\78\output_.jpg' does not contain an image sequence pattern or a pattern is invalid.
2025-06-29 00:34:19.253 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl : [image2 @ 0000020ac4665c00] Use a pattern such as %03d for an image sequence or use the -update option (with -frames:v 1 if needed) to write a single image.
2025-06-29 00:34:19.254 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl : [out#0/image2 @ 0000020ac4c8ee40] video:211KiB audio:0KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing overhead: unknown
2025-06-29 00:34:19.254 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl : frame=    1 fps=0.3 q=2.0 Lsize=N/A time=00:26:27.00 bitrate=N/A speed= 527x    
2025-06-29 00:34:19.277 [34mINFO [0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl : 命令成功执行。
2025-06-29 00:34:19.321 [34mINFO [0;39m [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-06-29 00:34:20.558 [39mDEBUG[0;39m [file-async-4] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-29 00:34:20.558 [39mDEBUG[0;39m [file-async-4] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@edd5822] was not registered for synchronization because synchronization is not active
2025-06-29 00:34:20.567 [39mDEBUG[0;39m [file-async-4] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1272956829 wrapping com.mysql.cj.jdbc.ConnectionImpl@3aa78972] will not be managed by Spring
2025-06-29 00:34:20.568 [39mDEBUG[0;39m [file-async-4] c.e.p.m.p.P.updateSpriteSheetObjectKey : ==>  Preparing: UPDATE video_episodes SET sprite_sheet_object_key = ?, updated_time = NOW() WHERE video_info_id = ? AND number = ?
2025-06-29 00:34:20.568 [39mDEBUG[0;39m [file-async-4] c.e.p.m.p.P.updateSpriteSheetObjectKey : ==> Parameters: video/bf7fae33-36bf-4db1-88d1-0ba08b475c32/.jpg(String), 78(Long), (String)
2025-06-29 00:34:20.590 [39mDEBUG[0;39m [file-async-4] c.e.p.m.p.P.updateSpriteSheetObjectKey : <==    Updates: 1
2025-06-29 00:34:20.590 [39mDEBUG[0;39m [file-async-4] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@edd5822]
2025-06-29 00:34:20.590 [34mINFO [0;39m [file-async-4] c.e.p.s.impl.PureVideoUrlServiceImpl : 异步生成雪碧图成功并已更新数据库 - videoId: 78, episode: , objectKey: video/bf7fae33-36bf-4db1-88d1-0ba08b475c32/.jpg
2025-06-29 00:34:23.499 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.security.web.FilterChainProxy : Securing POST /api/videoUrl/video-episodes
2025-06-29 00:34:23.499 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-29 00:34:23.500 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#uploadVideo(VideoInfoWithEpisodesDto)
2025-06-29 00:34:23.500 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.s.w.a.AnonymousAuthenticationFilter : Set SecurityContextHolder to anonymous SecurityContext
2025-06-29 00:34:23.500 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /api/videoUrl/video-episodes] with attributes [permitAll]
2025-06-29 00:34:23.500 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.security.web.FilterChainProxy : Secured POST /api/videoUrl/video-episodes
2025-06-29 00:34:23.500 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.web.servlet.DispatcherServlet : POST "/api/videoUrl/video-episodes", parameters={}
2025-06-29 00:34:23.501 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#uploadVideo(VideoInfoWithEpisodesDto)
2025-06-29 00:34:23.501 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [VideoInfoWithEpisodesDto(videoEpisodes=[VideoEpisodesDTO(id=null, videoInfoId=null, number=, objectK (truncated)...]
2025-06-29 00:34:23.524 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-29 00:34:23.524 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6bf24345]
2025-06-29 00:34:23.524 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1628055605 wrapping com.mysql.cj.jdbc.ConnectionImpl@3aa78972] will be managed by Spring
2025-06-29 00:34:23.524 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.P.findVideoInfoByTitle : ==>  Preparing: SELECT id, title, cover_image_url, description, created_time, updated_time FROM video_info WHERE title = ? LIMIT 1
2025-06-29 00:34:23.525 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.P.findVideoInfoByTitle : ==> Parameters: 薰香花朵凛然绽放(String)
2025-06-29 00:34:23.535 [39mDEBUG[0;39m [http-nio-8080-exec-5] c.e.p.m.p.P.findVideoInfoByTitle : <==      Total: 0
2025-06-29 00:34:23.536 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6bf24345]
2025-06-29 00:34:23.536 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6bf24345]
2025-06-29 00:34:23.536 [39mDEBUG[0;39m [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6bf24345]
2025-06-29 00:34:23.555 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : Using @ExceptionHandler com.example.pure.exception.GlobalExceptionHandler#handleBusinessException(BusinessException)
2025-06-29 00:34:23.556 [31mWARN [0;39m [http-nio-8080-exec-5] c.e.p.e.GlobalExceptionHandler : 业务异常: 查找不到视频信息
2025-06-29 00:34:23.556 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-29 00:34:23.557 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=600, message=查找不到视频信息, success=false, data=null, time=2025-06-28T16:34:23.556437800Z)]
2025-06-29 00:34:23.557 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : Resolved [com.example.pure.exception.BusinessException: 查找不到视频信息]
2025-06-29 00:34:23.558 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.web.servlet.DispatcherServlet : Completed 400 BAD_REQUEST
2025-06-29 00:34:23.558 [39mDEBUG[0;39m [http-nio-8080-exec-5] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-29 00:34:23.616 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.security.web.FilterChainProxy : Securing POST /api/videoUrl/videoinfo-with-episodes
2025-06-29 00:34:23.616 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-29 00:34:23.616 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#uploadVideoWithEpisodes(VideoInfoWithEpisodesDto)
2025-06-29 00:34:23.616 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.s.w.a.AnonymousAuthenticationFilter : Set SecurityContextHolder to anonymous SecurityContext
2025-06-29 00:34:23.616 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /api/videoUrl/videoinfo-with-episodes] with attributes [permitAll]
2025-06-29 00:34:23.617 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.security.web.FilterChainProxy : Secured POST /api/videoUrl/videoinfo-with-episodes
2025-06-29 00:34:23.617 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.web.servlet.DispatcherServlet : POST "/api/videoUrl/videoinfo-with-episodes", parameters={}
2025-06-29 00:34:23.618 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#uploadVideoWithEpisodes(VideoInfoWithEpisodesDto)
2025-06-29 00:34:23.619 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [VideoInfoWithEpisodesDto(videoEpisodes=[VideoEpisodesDTO(id=null, videoInfoId=null, number=, objectK (truncated)...]
2025-06-29 00:34:23.630 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-29 00:34:23.630 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@208c1bda]
2025-06-29 00:34:23.631 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@712274090 wrapping com.mysql.cj.jdbc.ConnectionImpl@3aa78972] will be managed by Spring
2025-06-29 00:34:23.631 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.P.findVideoInfoByTitle : ==>  Preparing: SELECT id, title, cover_image_url, description, created_time, updated_time FROM video_info WHERE title = ? LIMIT 1
2025-06-29 00:34:23.631 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.P.findVideoInfoByTitle : ==> Parameters: 薰香花朵凛然绽放(String)
2025-06-29 00:34:23.642 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.P.findVideoInfoByTitle : <==      Total: 0
2025-06-29 00:34:23.642 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@208c1bda]
2025-06-29 00:34:23.643 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@208c1bda] from current transaction
2025-06-29 00:34:23.643 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.P.insertVideoInfo : ==>  Preparing: INSERT INTO video_info( title, cover_image_url, description, created_time, updated_time ) VALUES ( ?, ?, ?, NOW(), NOW() )
2025-06-29 00:34:23.643 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.P.insertVideoInfo : ==> Parameters: 薰香花朵凛然绽放(String), https://lain.bgm.tv/r/400/pic/cover/l/b8/0d/513345_jv4wM.jpg(String), 窗帘彼端，是与我永远无缘的世界。学风不佳的千鸟男校，与名门贵族女校桔梗学院势同水火。两校教室虽仅一窗之隔，分隔的窗帘却永不拉开。就读千鸟的凛太郎，因外表常被误认为“可怕”而遭人疏远，渐渐变得独来独往。某日帮家中蛋糕店时，他遇见了顾客薰子。“我啊，从不觉得凛太郎君可怕哦。”薰子不带偏见的善意让凛太郎既困惑又温暖。然而，他很快发现薰子竟是桔梗的学生……隔着一道冰冷的窗帘，近在咫尺却遥不可及的两人，即将编织出一段绚烂的青春物语。(String)
2025-06-29 00:34:23.663 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.P.insertVideoInfo : <==    Updates: 1
2025-06-29 00:34:23.664 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@208c1bda]
2025-06-29 00:34:23.664 [34mINFO [0;39m [http-nio-8080-exec-6] c.e.p.s.impl.PureVideoUrlServiceImpl : 插入视频信息成功：薰香花朵凛然绽放
2025-06-29 00:34:23.666 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@208c1bda] from current transaction
2025-06-29 00:34:23.666 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.P.findVideoInfoByTitle : ==>  Preparing: SELECT id, title, cover_image_url, description, created_time, updated_time FROM video_info WHERE title = ? LIMIT 1
2025-06-29 00:34:23.666 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.P.findVideoInfoByTitle : ==> Parameters: 薰香花朵凛然绽放(String)
2025-06-29 00:34:23.678 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.P.findVideoInfoByTitle : <==      Total: 1
2025-06-29 00:34:23.678 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@208c1bda]
2025-06-29 00:34:23.678 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@208c1bda] from current transaction
2025-06-29 00:34:23.678 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.P.findVideoTypeByName : ==>  Preparing: SELECT * FROM video_type WHERE name = ?
2025-06-29 00:34:23.679 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.P.findVideoTypeByName : ==> Parameters: 2025(String)
2025-06-29 00:34:23.690 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.P.findVideoTypeByName : <==      Total: 1
2025-06-29 00:34:23.690 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@208c1bda]
2025-06-29 00:34:23.690 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@208c1bda] from current transaction
2025-06-29 00:34:23.690 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.P.findVideoTypeByName : ==>  Preparing: SELECT * FROM video_type WHERE name = ?
2025-06-29 00:34:23.691 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.P.findVideoTypeByName : ==> Parameters: 日本(String)
2025-06-29 00:34:23.701 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.P.findVideoTypeByName : <==      Total: 1
2025-06-29 00:34:23.701 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@208c1bda]
2025-06-29 00:34:23.701 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@208c1bda] from current transaction
2025-06-29 00:34:23.701 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.P.findVideoTypeByName : ==>  Preparing: SELECT * FROM video_type WHERE name = ?
2025-06-29 00:34:23.702 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.P.findVideoTypeByName : ==> Parameters: 校园(String)
2025-06-29 00:34:23.712 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.P.findVideoTypeByName : <==      Total: 1
2025-06-29 00:34:23.713 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@208c1bda]
2025-06-29 00:34:23.713 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@208c1bda] from current transaction
2025-06-29 00:34:23.713 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.P.findVideoTypeByName : ==>  Preparing: SELECT * FROM video_type WHERE name = ?
2025-06-29 00:34:23.713 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.P.findVideoTypeByName : ==> Parameters: 青春(String)
2025-06-29 00:34:23.725 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.P.findVideoTypeByName : <==      Total: 1
2025-06-29 00:34:23.725 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@208c1bda]
2025-06-29 00:34:23.725 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@208c1bda] from current transaction
2025-06-29 00:34:23.725 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.P.findVideoTypeByName : ==>  Preparing: SELECT * FROM video_type WHERE name = ?
2025-06-29 00:34:23.726 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.P.findVideoTypeByName : ==> Parameters: 治愈(String)
2025-06-29 00:34:23.736 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.P.findVideoTypeByName : <==      Total: 1
2025-06-29 00:34:23.736 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@208c1bda]
2025-06-29 00:34:23.736 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@208c1bda] from current transaction
2025-06-29 00:34:23.736 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.P.findVideoTypeByName : ==>  Preparing: SELECT * FROM video_type WHERE name = ?
2025-06-29 00:34:23.737 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.P.findVideoTypeByName : ==> Parameters: 恋爱(String)
2025-06-29 00:34:23.747 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.P.findVideoTypeByName : <==      Total: 1
2025-06-29 00:34:23.748 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@208c1bda]
2025-06-29 00:34:23.748 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@208c1bda] from current transaction
2025-06-29 00:34:23.748 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.P.findVideoTypeByName : ==>  Preparing: SELECT * FROM video_type WHERE name = ?
2025-06-29 00:34:23.748 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.P.findVideoTypeByName : ==> Parameters: 日本动漫(String)
2025-06-29 00:34:23.759 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.P.findVideoTypeByName : <==      Total: 1
2025-06-29 00:34:23.759 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@208c1bda]
2025-06-29 00:34:23.759 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@208c1bda] from current transaction
2025-06-29 00:34:23.760 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.P.insertVideoInfoTypeLink : ==>  Preparing: INSERT INTO video_info_type_link( video_info_id, type_id, created_time, updated_time ) VALUES ( ?, ?, NOW(), NOW() ) , ( ?, ?, NOW(), NOW() ) , ( ?, ?, NOW(), NOW() ) , ( ?, ?, NOW(), NOW() ) , ( ?, ?, NOW(), NOW() ) , ( ?, ?, NOW(), NOW() ) , ( ?, ?, NOW(), NOW() )
2025-06-29 00:34:23.761 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.P.insertVideoInfoTypeLink : ==> Parameters: 79(Long), 36(Long), 79(Long), 30(Long), 79(Long), 42(Long), 79(Long), 38(Long), 79(Long), 50(Long), 79(Long), 33(Long), 79(Long), 35(Long)
2025-06-29 00:34:23.781 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.P.insertVideoInfoTypeLink : <==    Updates: 7
2025-06-29 00:34:23.782 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@208c1bda]
2025-06-29 00:34:23.782 [34mINFO [0;39m [http-nio-8080-exec-6] c.e.p.s.impl.PureVideoUrlServiceImpl : 插入视频类型关联信息成功，视频ID: 79
2025-06-29 00:34:23.783 [34mINFO [0;39m [file-async-5] c.e.p.s.impl.PureVideoUrlServiceImpl : 开始异步下载并上传视频文件 - videoId: 79, episode: , url: http://p16-va-tiktok.ibyteimg.com/obj/tos-alisg-v-0051c001-sg/oMHNIRBoBgBIHiNETEvAfgLAeBMDXFjAhQZEgD
2025-06-29 00:34:23.783 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@208c1bda] from current transaction
2025-06-29 00:34:23.783 [34mINFO [0;39m [file-async-6] c.e.p.s.impl.PureVideoUrlServiceImpl : 开始异步生成雪碧图 - videoId: 79, episode: 
2025-06-29 00:34:23.783 [39mDEBUG[0;39m [file-async-5] o.s.w.r.f.client.ExchangeFunctions : [a7d75cd] HTTP GET http://p16-va-tiktok.ibyteimg.com/obj/tos-alisg-v-0051c001-sg/oMHNIRBoBgBIHiNETEvAfgLAeBMDXFjAhQZEgD
2025-06-29 00:34:23.783 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.P.insertVideoEpisodes : ==>  Preparing: INSERT INTO video_episodes( video_info_id, number, object_key, duration, sprite_sheet_object_key, created_time, updated_time ) VALUES ( ?, ?, ?, ?, ?, NOW(), NOW() )
2025-06-29 00:34:23.784 [34mINFO [0;39m [file-async-6] c.e.p.service.impl.FFmpegServiceImpl : Executing command: ffmpeg -i http://p16-va-tiktok.ibyteimg.com/obj/tos-alisg-v-0051c001-sg/oMHNIRBoBgBIHiNETEvAfgLAeBMDXFjAhQZEgD -vf fps=1/3,scale=160:-1,tile=23x23 -q:v 2 -y d:\upload\video_sprites\79\output_.jpg
2025-06-29 00:34:23.784 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.P.insertVideoEpisodes : ==> Parameters: 79(Long), (String), null, null, null
2025-06-29 00:34:23.804 [39mDEBUG[0;39m [http-nio-8080-exec-6] c.e.p.m.p.P.insertVideoEpisodes : <==    Updates: 1
2025-06-29 00:34:23.805 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@208c1bda]
2025-06-29 00:34:23.805 [34mINFO [0;39m [http-nio-8080-exec-6] c.e.p.s.impl.PureVideoUrlServiceImpl : 插入视频分集信息成功，共1集，视频ID: 79
2025-06-29 00:34:23.805 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@208c1bda]
2025-06-29 00:34:23.805 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@208c1bda]
2025-06-29 00:34:23.805 [39mDEBUG[0;39m [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@208c1bda]
2025-06-29 00:34:23.827 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : Using @ExceptionHandler com.example.pure.exception.GlobalExceptionHandler#handleException(Exception)
2025-06-29 00:34:23.827 [39mDEBUG[0;39m [file-async-6] c.e.p.service.impl.FFmpegServiceImpl : ffmpeg version 7.1.1-full_build-www.gyan.dev Copyright (c) 2000-2025 the FFmpeg developers
2025-06-29 00:34:23.827 [39mDEBUG[0;39m [file-async-6] c.e.p.service.impl.FFmpegServiceImpl :   built with gcc 14.2.0 (Rev1, Built by MSYS2 project)
2025-06-29 00:34:23.827 [39mDEBUG[0;39m [file-async-6] c.e.p.service.impl.FFmpegServiceImpl :   configuration: --enable-gpl --enable-version3 --enable-static --disable-w32threads --disable-autodetect --enable-fontconfig --enable-iconv --enable-gnutls --enable-lcms2 --enable-libxml2 --enable-gmp --enable-bzlib --enable-lzma --enable-libsnappy --enable-zlib --enable-librist --enable-libsrt --enable-libssh --enable-libzmq --enable-avisynth --enable-libbluray --enable-libcaca --enable-libdvdnav --enable-libdvdread --enable-sdl2 --enable-libaribb24 --enable-libaribcaption --enable-libdav1d --enable-libdavs2 --enable-libopenjpeg --enable-libquirc --enable-libuavs3d --enable-libxevd --enable-libzvbi --enable-libqrencode --enable-librav1e --enable-libsvtav1 --enable-libvvenc --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxavs2 --enable-libxeve --enable-libxvid --enable-libaom --enable-libjxl --enable-libvpx --enable-mediafoundation --enable-libass --enable-frei0r --enable-libfreetype --enable-libfribidi --enable-libharfbuzz --enable-liblensfun --enable-libvidstab --enable-libvmaf --enable-libzimg --enable-amf --enable-cuda-llvm --enable-cuvid --enable-dxva2 --enable-d3d11va --enable-d3d12va --enable-ffnvcodec --enable-libvpl --enable-nvdec --enable-nvenc --enable-vaapi --enable-libshaderc --enable-vulkan --enable-libplacebo --enable-opencl --enable-libcdio --enable-libgme --enable-libmodplug --enable-libopenmpt --enable-libopencore-amrwb --enable-libmp3lame --enable-libshine --enable-libtheora --enable-libtwolame --enable-libvo-amrwbenc --enable-libcodec2 --enable-libilbc --enable-libgsm --enable-liblc3 --enable-libopencore-amrnb --enable-libopus --enable-libspeex --enable-libvorbis --enable-ladspa --enable-libbs2b --enable-libflite --enable-libmysofa --enable-librubberband --enable-libsoxr --enable-chromaprint
2025-06-29 00:34:23.827 [39mDEBUG[0;39m [file-async-6] c.e.p.service.impl.FFmpegServiceImpl :   libavutil      59. 39.100 / 59. 39.100
2025-06-29 00:34:23.827 [39mDEBUG[0;39m [file-async-6] c.e.p.service.impl.FFmpegServiceImpl :   libavcodec     61. 19.101 / 61. 19.101
2025-06-29 00:34:23.827 [39mDEBUG[0;39m [file-async-6] c.e.p.service.impl.FFmpegServiceImpl :   libavformat    61.  7.100 / 61.  7.100
2025-06-29 00:34:23.827 [39mDEBUG[0;39m [file-async-6] c.e.p.service.impl.FFmpegServiceImpl :   libavdevice    61.  3.100 / 61.  3.100
2025-06-29 00:34:23.827 [39mDEBUG[0;39m [file-async-6] c.e.p.service.impl.FFmpegServiceImpl :   libavfilter    10.  4.100 / 10.  4.100
2025-06-29 00:34:23.827 [39mDEBUG[0;39m [file-async-6] c.e.p.service.impl.FFmpegServiceImpl :   libswscale      8.  3.100 /  8.  3.100
2025-06-29 00:34:23.827 [39mDEBUG[0;39m [file-async-6] c.e.p.service.impl.FFmpegServiceImpl :   libswresample   5.  3.100 /  5.  3.100
2025-06-29 00:34:23.827 [39mDEBUG[0;39m [file-async-6] c.e.p.service.impl.FFmpegServiceImpl :   libpostproc    58.  3.100 / 58.  3.100
2025-06-29 00:34:23.827 [1;31mERROR[0;39m [http-nio-8080-exec-6] c.e.p.e.GlobalExceptionHandler : 服务器处理API出现异常
java.lang.IllegalArgumentException: Cache 'videoInfo' does not allow 'null' values. Avoid storing null via '@Cacheable(unless="#result == null")' or configure RedisCache to allow 'null' via RedisCacheConfiguration.
	at org.springframework.data.redis.cache.RedisCache.put(RedisCache.java:166)
	at org.springframework.cache.transaction.TransactionAwareCacheDecorator$1.afterCommit(TransactionAwareCacheDecorator.java:100)
	at org.springframework.transaction.support.TransactionSynchronizationUtils.invokeAfterCommit(TransactionSynchronizationUtils.java:135)
	at org.springframework.transaction.support.TransactionSynchronizationUtils.triggerAfterCommit(TransactionSynchronizationUtils.java:123)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.triggerAfterCommit(AbstractPlatformTransactionManager.java:936)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.processCommit(AbstractPlatformTransactionManager.java:782)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.commit(AbstractPlatformTransactionManager.java:711)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.commitTransactionAfterReturning(TransactionAspectSupport.java:654)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:407)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.service.impl.PureVideoUrlServiceImpl$$EnhancerBySpringCGLIB$$7754b21f.uploadVideoWithEpisodes(<generated>)
	at com.example.pure.controller.PureVideoUrlController.uploadVideoWithEpisodes(PureVideoUrlController.java:42)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-06-29 00:34:23.828 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-29 00:34:23.828 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Writing [Result(code=500, message=服务器处理API出现异常，请联系管理员, success=false, data=null, time=2025-06-28T16:34:23.828 (truncated)...]
2025-06-29 00:34:23.829 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : Resolved [java.lang.IllegalArgumentException: Cache 'videoInfo' does not allow 'null' values. Avoid storing null via '@Cacheable(unless="#result == null")' or configure RedisCache to allow 'null' via RedisCacheConfiguration.]
2025-06-29 00:34:23.829 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.web.servlet.DispatcherServlet : Completed 500 INTERNAL_SERVER_ERROR
2025-06-29 00:34:23.829 [39mDEBUG[0;39m [http-nio-8080-exec-6] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-29 00:34:23.958 [39mDEBUG[0;39m [reactor-http-nio-2] o.s.w.r.f.client.ExchangeFunctions : [a7d75cd] [a0608cac-3] Response 200 OK
2025-06-29 00:34:25.494 [39mDEBUG[0;39m [file-async-6] c.e.p.service.impl.FFmpegServiceImpl : Input #0, mov,mp4,m4a,3gp,3g2,mj2, from 'http://p16-va-tiktok.ibyteimg.com/obj/tos-alisg-v-0051c001-sg/oMHNIRBoBgBIHiNETEvAfgLAeBMDXFjAhQZEgD':
2025-06-29 00:34:25.494 [39mDEBUG[0;39m [file-async-6] c.e.p.service.impl.FFmpegServiceImpl :   Metadata:
2025-06-29 00:34:25.494 [39mDEBUG[0;39m [file-async-6] c.e.p.service.impl.FFmpegServiceImpl :     major_brand     : isom
2025-06-29 00:34:25.494 [39mDEBUG[0;39m [file-async-6] c.e.p.service.impl.FFmpegServiceImpl :     minor_version   : 512
2025-06-29 00:34:25.494 [39mDEBUG[0;39m [file-async-6] c.e.p.service.impl.FFmpegServiceImpl :     compatible_brands: isomiso2avc1mp41
2025-06-29 00:34:25.494 [39mDEBUG[0;39m [file-async-6] c.e.p.service.impl.FFmpegServiceImpl :     encoder         : Lavf57.83.100
2025-06-29 00:34:25.494 [39mDEBUG[0;39m [file-async-6] c.e.p.service.impl.FFmpegServiceImpl :     description     : Packed by Bilibili XCoder v2.0.2
2025-06-29 00:34:25.494 [39mDEBUG[0;39m [file-async-6] c.e.p.service.impl.FFmpegServiceImpl :   Duration: 00:01:38.06, start: 0.000000, bitrate: 813 kb/s
2025-06-29 00:34:25.494 [39mDEBUG[0;39m [file-async-6] c.e.p.service.impl.FFmpegServiceImpl :   Stream #0:0[0x1](und): Video: h264 (High) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 1920x1080 [SAR 1:1 DAR 16:9], 720 kb/s, 23.97 fps, 23.98 tbr, 16k tbn (default)
2025-06-29 00:34:25.494 [39mDEBUG[0;39m [file-async-6] c.e.p.service.impl.FFmpegServiceImpl :       Metadata:
2025-06-29 00:34:25.494 [39mDEBUG[0;39m [file-async-6] c.e.p.service.impl.FFmpegServiceImpl :         handler_name    : VideoHandler
2025-06-29 00:34:25.494 [39mDEBUG[0;39m [file-async-6] c.e.p.service.impl.FFmpegServiceImpl :         vendor_id       : [0][0][0][0]
2025-06-29 00:34:25.494 [39mDEBUG[0;39m [file-async-6] c.e.p.service.impl.FFmpegServiceImpl :   Stream #0:1[0x2](und): Audio: aac (LC) (mp4a / 0x6134706D), 44100 Hz, stereo, fltp, 87 kb/s (default)
2025-06-29 00:34:25.494 [39mDEBUG[0;39m [file-async-6] c.e.p.service.impl.FFmpegServiceImpl :       Metadata:
2025-06-29 00:34:25.494 [39mDEBUG[0;39m [file-async-6] c.e.p.service.impl.FFmpegServiceImpl :         handler_name    : SoundHandler
2025-06-29 00:34:25.494 [39mDEBUG[0;39m [file-async-6] c.e.p.service.impl.FFmpegServiceImpl :         vendor_id       : [0][0][0][0]
2025-06-29 00:34:25.499 [39mDEBUG[0;39m [file-async-6] c.e.p.service.impl.FFmpegServiceImpl : Stream mapping:
2025-06-29 00:34:25.499 [39mDEBUG[0;39m [file-async-6] c.e.p.service.impl.FFmpegServiceImpl :   Stream #0:0 -> #0:0 (h264 (native) -> mjpeg (native))
2025-06-29 00:34:25.499 [39mDEBUG[0;39m [file-async-6] c.e.p.service.impl.FFmpegServiceImpl : Press [q] to stop, [?] for help
2025-06-29 00:34:26.006 [39mDEBUG[0;39m [reactor-http-nio-2] org.springframework.web.HttpLogging : [a7d75cd] [a0608cac-3] Read 9974702 bytes
2025-06-29 00:34:26.006 [34mINFO [0;39m [file-async-5] c.e.p.s.impl.PureVideoUrlServiceImpl : 视频文件下载完成 - videoId: 79, episode: , 文件大小: 9974702 bytes
2025-06-29 00:34:28.266 [39mDEBUG[0;39m [file-async-6] c.e.p.service.impl.FFmpegServiceImpl : Output #0, image2, to 'd:\upload\video_sprites\79\output_.jpg':
2025-06-29 00:34:28.266 [39mDEBUG[0;39m [file-async-6] c.e.p.service.impl.FFmpegServiceImpl :   Metadata:
2025-06-29 00:34:28.266 [39mDEBUG[0;39m [file-async-6] c.e.p.service.impl.FFmpegServiceImpl :     major_brand     : isom
2025-06-29 00:34:28.266 [39mDEBUG[0;39m [file-async-6] c.e.p.service.impl.FFmpegServiceImpl :     minor_version   : 512
2025-06-29 00:34:28.266 [39mDEBUG[0;39m [file-async-6] c.e.p.service.impl.FFmpegServiceImpl :     compatible_brands: isomiso2avc1mp41
2025-06-29 00:34:28.266 [39mDEBUG[0;39m [file-async-6] c.e.p.service.impl.FFmpegServiceImpl :     description     : Packed by Bilibili XCoder v2.0.2
2025-06-29 00:34:28.266 [39mDEBUG[0;39m [file-async-6] c.e.p.service.impl.FFmpegServiceImpl :     encoder         : Lavf61.7.100
2025-06-29 00:34:28.266 [39mDEBUG[0;39m [file-async-6] c.e.p.service.impl.FFmpegServiceImpl :   Stream #0:0(und): Video: mjpeg, yuv420p(pc, bt709, progressive), 3680x2070 [SAR 1:1 DAR 16:9], q=2-31, 200 kb/s, 0.0006 fps, 0.0006 tbn (default)
2025-06-29 00:34:28.266 [39mDEBUG[0;39m [file-async-6] c.e.p.service.impl.FFmpegServiceImpl :       Metadata:
2025-06-29 00:34:28.266 [39mDEBUG[0;39m [file-async-6] c.e.p.service.impl.FFmpegServiceImpl :         handler_name    : VideoHandler
2025-06-29 00:34:28.266 [39mDEBUG[0;39m [file-async-6] c.e.p.service.impl.FFmpegServiceImpl :         vendor_id       : [0][0][0][0]
2025-06-29 00:34:28.266 [39mDEBUG[0;39m [file-async-6] c.e.p.service.impl.FFmpegServiceImpl :         encoder         : Lavc61.19.101 mjpeg
2025-06-29 00:34:28.266 [39mDEBUG[0;39m [file-async-6] c.e.p.service.impl.FFmpegServiceImpl :       Side data:
2025-06-29 00:34:28.266 [39mDEBUG[0;39m [file-async-6] c.e.p.service.impl.FFmpegServiceImpl :         cpb: bitrate max/min/avg: 0/0/200000 buffer size: 0 vbv_delay: N/A
2025-06-29 00:34:28.288 [39mDEBUG[0;39m [file-async-6] c.e.p.service.impl.FFmpegServiceImpl : [image2 @ 000002e901819d40] The specified filename 'd:\upload\video_sprites\79\output_.jpg' does not contain an image sequence pattern or a pattern is invalid.
2025-06-29 00:34:28.288 [39mDEBUG[0;39m [file-async-6] c.e.p.service.impl.FFmpegServiceImpl : [image2 @ 000002e901819d40] Use a pattern such as %03d for an image sequence or use the -update option (with -frames:v 1 if needed) to write a single image.
2025-06-29 00:34:28.289 [39mDEBUG[0;39m [file-async-6] c.e.p.service.impl.FFmpegServiceImpl : [out#0/image2 @ 000002e97fffe900] video:202KiB audio:0KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing overhead: unknown
2025-06-29 00:34:28.289 [39mDEBUG[0;39m [file-async-6] c.e.p.service.impl.FFmpegServiceImpl : frame=    1 fps=0.4 q=2.0 Lsize=N/A time=00:26:27.00 bitrate=N/A speed= 569x    
2025-06-29 00:34:28.317 [34mINFO [0;39m [file-async-6] c.e.p.service.impl.FFmpegServiceImpl : 命令成功执行。
2025-06-29 00:34:29.172 [34mINFO [0;39m [file-async-5] c.e.p.s.impl.PureVideoUrlServiceImpl : 视频文件上传到R2成功 - videoId: 79, episode: , objectKey: video/a9801524-4a98-4a3c-b5a3-de4ab8adb31d/.mp4
2025-06-29 00:34:29.172 [39mDEBUG[0;39m [file-async-5] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-29 00:34:29.172 [39mDEBUG[0;39m [file-async-5] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4ba9af2] was not registered for synchronization because synchronization is not active
2025-06-29 00:34:29.182 [39mDEBUG[0;39m [file-async-5] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@8724157 wrapping com.mysql.cj.jdbc.ConnectionImpl@3aa78972] will not be managed by Spring
2025-06-29 00:34:29.182 [39mDEBUG[0;39m [file-async-5] c.e.p.m.p.P.updateEpisodeObjectKey : ==>  Preparing: UPDATE video_episodes SET object_key = ?, updated_time = NOW() WHERE video_info_id = ? AND number = ?
2025-06-29 00:34:29.182 [39mDEBUG[0;39m [file-async-5] c.e.p.m.p.P.updateEpisodeObjectKey : ==> Parameters: video/a9801524-4a98-4a3c-b5a3-de4ab8adb31d/.mp4(String), 79(Long), (String)
2025-06-29 00:34:29.204 [39mDEBUG[0;39m [file-async-5] c.e.p.m.p.P.updateEpisodeObjectKey : <==    Updates: 1
2025-06-29 00:34:29.204 [39mDEBUG[0;39m [file-async-5] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4ba9af2]
2025-06-29 00:34:29.204 [34mINFO [0;39m [file-async-5] c.e.p.s.impl.PureVideoUrlServiceImpl : 异步更新数据库objectKey成功 - videoId: 79, episode: , objectKey: video/a9801524-4a98-4a3c-b5a3-de4ab8adb31d/.mp4
2025-06-29 00:34:30.210 [39mDEBUG[0;39m [file-async-6] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-29 00:34:30.210 [39mDEBUG[0;39m [file-async-6] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@62cd540d] was not registered for synchronization because synchronization is not active
2025-06-29 00:34:30.220 [39mDEBUG[0;39m [file-async-6] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@571211607 wrapping com.mysql.cj.jdbc.ConnectionImpl@3aa78972] will not be managed by Spring
2025-06-29 00:34:30.220 [39mDEBUG[0;39m [file-async-6] c.e.p.m.p.P.updateSpriteSheetObjectKey : ==>  Preparing: UPDATE video_episodes SET sprite_sheet_object_key = ?, updated_time = NOW() WHERE video_info_id = ? AND number = ?
2025-06-29 00:34:30.221 [39mDEBUG[0;39m [file-async-6] c.e.p.m.p.P.updateSpriteSheetObjectKey : ==> Parameters: video/a9801524-4a98-4a3c-b5a3-de4ab8adb31d/.jpg(String), 79(Long), (String)
2025-06-29 00:34:30.243 [39mDEBUG[0;39m [file-async-6] c.e.p.m.p.P.updateSpriteSheetObjectKey : <==    Updates: 1
2025-06-29 00:34:30.243 [39mDEBUG[0;39m [file-async-6] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@62cd540d]
2025-06-29 00:34:30.243 [34mINFO [0;39m [file-async-6] c.e.p.s.impl.PureVideoUrlServiceImpl : 异步生成雪碧图成功并已更新数据库 - videoId: 79, episode: , objectKey: video/a9801524-4a98-4a3c-b5a3-de4ab8adb31d/.jpg
2025-06-29 00:34:39.614 [34mINFO [0;39m [file-async-3] c.e.p.s.impl.PureVideoUrlServiceImpl : 视频文件上传到R2成功 - videoId: 78, episode: , objectKey: video/bf7fae33-36bf-4db1-88d1-0ba08b475c32/.mp4
2025-06-29 00:34:39.614 [39mDEBUG[0;39m [file-async-3] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-29 00:34:39.614 [39mDEBUG[0;39m [file-async-3] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@60526904] was not registered for synchronization because synchronization is not active
2025-06-29 00:34:39.623 [39mDEBUG[0;39m [file-async-3] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@626064234 wrapping com.mysql.cj.jdbc.ConnectionImpl@3aa78972] will not be managed by Spring
2025-06-29 00:34:39.623 [39mDEBUG[0;39m [file-async-3] c.e.p.m.p.P.updateEpisodeObjectKey : ==>  Preparing: UPDATE video_episodes SET object_key = ?, updated_time = NOW() WHERE video_info_id = ? AND number = ?
2025-06-29 00:34:39.623 [39mDEBUG[0;39m [file-async-3] c.e.p.m.p.P.updateEpisodeObjectKey : ==> Parameters: video/bf7fae33-36bf-4db1-88d1-0ba08b475c32/.mp4(String), 78(Long), (String)
2025-06-29 00:34:39.644 [39mDEBUG[0;39m [file-async-3] c.e.p.m.p.P.updateEpisodeObjectKey : <==    Updates: 1
2025-06-29 00:34:39.645 [39mDEBUG[0;39m [file-async-3] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@60526904]
2025-06-29 00:34:39.645 [34mINFO [0;39m [file-async-3] c.e.p.s.impl.PureVideoUrlServiceImpl : 异步更新数据库objectKey成功 - videoId: 78, episode: , objectKey: video/bf7fae33-36bf-4db1-88d1-0ba08b475c32/.mp4
2025-06-29 00:46:52.671 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.security.web.FilterChainProxy : Securing POST /api/videoUrl/video-episodes
2025-06-29 00:46:52.671 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-29 00:46:52.672 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#uploadVideo(VideoInfoWithEpisodesDto)
2025-06-29 00:46:52.672 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.s.w.a.AnonymousAuthenticationFilter : Set SecurityContextHolder to anonymous SecurityContext
2025-06-29 00:46:52.673 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /api/videoUrl/video-episodes] with attributes [permitAll]
2025-06-29 00:46:52.673 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.security.web.FilterChainProxy : Secured POST /api/videoUrl/video-episodes
2025-06-29 00:46:52.673 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.web.servlet.DispatcherServlet : POST "/api/videoUrl/video-episodes", parameters={}
2025-06-29 00:46:52.673 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#uploadVideo(VideoInfoWithEpisodesDto)
2025-06-29 00:46:52.674 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [VideoInfoWithEpisodesDto(videoEpisodes=[VideoEpisodesDTO(id=null, videoInfoId=null, number=1, object (truncated)...]
2025-06-29 00:46:52.683 [31mWARN [0;39m [http-nio-8080-exec-7] com.zaxxer.hikari.pool.PoolBase : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@3aa78972 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-29 00:46:52.685 [31mWARN [0;39m [http-nio-8080-exec-7] com.zaxxer.hikari.pool.PoolBase : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@3711f8b8 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-29 00:46:52.686 [31mWARN [0;39m [http-nio-8080-exec-7] com.zaxxer.hikari.pool.PoolBase : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@ca2976 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-29 00:46:52.688 [31mWARN [0;39m [http-nio-8080-exec-7] com.zaxxer.hikari.pool.PoolBase : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@512d8f26 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-29 00:46:52.689 [31mWARN [0;39m [http-nio-8080-exec-7] com.zaxxer.hikari.pool.PoolBase : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@7e33ce47 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-29 00:46:52.690 [31mWARN [0;39m [http-nio-8080-exec-7] com.zaxxer.hikari.pool.PoolBase : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@46df3d01 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-29 00:46:52.692 [31mWARN [0;39m [http-nio-8080-exec-7] com.zaxxer.hikari.pool.PoolBase : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@178f3d35 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-29 00:46:52.693 [31mWARN [0;39m [http-nio-8080-exec-7] com.zaxxer.hikari.pool.PoolBase : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@2a66d45d (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-29 00:46:52.694 [31mWARN [0;39m [http-nio-8080-exec-7] com.zaxxer.hikari.pool.PoolBase : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@4a4776a0 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-29 00:46:52.695 [31mWARN [0;39m [http-nio-8080-exec-7] com.zaxxer.hikari.pool.PoolBase : HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@754f6ddc (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-29 00:46:52.871 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-29 00:46:52.871 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1b4780d5]
2025-06-29 00:46:52.871 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1981607913 wrapping com.mysql.cj.jdbc.ConnectionImpl@78b35d7b] will be managed by Spring
2025-06-29 00:46:52.871 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.P.findVideoInfoByTitle : ==>  Preparing: SELECT id, title, cover_image_url, description, created_time, updated_time FROM video_info WHERE title = ? LIMIT 1
2025-06-29 00:46:52.871 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.P.findVideoInfoByTitle : ==> Parameters: 碧蓝之海第二季(String)
2025-06-29 00:46:52.884 [39mDEBUG[0;39m [http-nio-8080-exec-7] c.e.p.m.p.P.findVideoInfoByTitle : <==      Total: 0
2025-06-29 00:46:52.884 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1b4780d5]
2025-06-29 00:46:52.884 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1b4780d5]
2025-06-29 00:46:52.884 [39mDEBUG[0;39m [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1b4780d5]
2025-06-29 00:46:52.907 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : Using @ExceptionHandler com.example.pure.exception.GlobalExceptionHandler#handleBusinessException(BusinessException)
2025-06-29 00:46:52.907 [31mWARN [0;39m [http-nio-8080-exec-7] c.e.p.e.GlobalExceptionHandler : 业务异常: 查找不到视频信息
2025-06-29 00:46:52.907 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-29 00:46:52.907 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=600, message=查找不到视频信息, success=false, data=null, time=2025-06-28T16:46:52.907125Z)]
2025-06-29 00:46:52.908 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : Resolved [com.example.pure.exception.BusinessException: 查找不到视频信息]
2025-06-29 00:46:52.908 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.web.servlet.DispatcherServlet : Completed 400 BAD_REQUEST
2025-06-29 00:46:52.908 [39mDEBUG[0;39m [http-nio-8080-exec-7] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-29 00:46:52.979 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.security.web.FilterChainProxy : Securing POST /api/videoUrl/videoinfo-with-episodes
2025-06-29 00:46:52.979 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-29 00:46:52.980 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#uploadVideoWithEpisodes(VideoInfoWithEpisodesDto)
2025-06-29 00:46:52.980 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.s.w.a.AnonymousAuthenticationFilter : Set SecurityContextHolder to anonymous SecurityContext
2025-06-29 00:46:52.980 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /api/videoUrl/videoinfo-with-episodes] with attributes [permitAll]
2025-06-29 00:46:52.980 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.security.web.FilterChainProxy : Secured POST /api/videoUrl/videoinfo-with-episodes
2025-06-29 00:46:52.980 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.web.servlet.DispatcherServlet : POST "/api/videoUrl/videoinfo-with-episodes", parameters={}
2025-06-29 00:46:52.981 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#uploadVideoWithEpisodes(VideoInfoWithEpisodesDto)
2025-06-29 00:46:52.981 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [VideoInfoWithEpisodesDto(videoEpisodes=[VideoEpisodesDTO(id=null, videoInfoId=null, number=1, object (truncated)...]
2025-06-29 00:46:52.997 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-29 00:46:52.997 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@a7cd1c9]
2025-06-29 00:46:52.997 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@8698553 wrapping com.mysql.cj.jdbc.ConnectionImpl@78b35d7b] will be managed by Spring
2025-06-29 00:46:52.997 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.P.findVideoInfoByTitle : ==>  Preparing: SELECT id, title, cover_image_url, description, created_time, updated_time FROM video_info WHERE title = ? LIMIT 1
2025-06-29 00:46:52.998 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.P.findVideoInfoByTitle : ==> Parameters: 碧蓝之海第二季(String)
2025-06-29 00:46:53.010 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.P.findVideoInfoByTitle : <==      Total: 0
2025-06-29 00:46:53.010 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@a7cd1c9]
2025-06-29 00:46:53.010 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@a7cd1c9] from current transaction
2025-06-29 00:46:53.010 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.P.insertVideoInfo : ==>  Preparing: INSERT INTO video_info( title, cover_image_url, description, created_time, updated_time ) VALUES ( ?, ?, ?, NOW(), NOW() )
2025-06-29 00:46:53.011 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.P.insertVideoInfo : ==> Parameters: 碧蓝之海第二季(String), https://lain.bgm.tv/r/400/pic/cover/l/d6/29/515880_96Oxr.jpg(String), 我们的夏天不会结束！北原伊织在伊豆开始大学生活已经快三个月了在潜水店“碧海蓝天”与可爱的表妹古手川千纱住在一个屋檐下。……大学生活的大半都是全裸混蛋们的疯狂骚动！伊织加入的潜水社团“Peek a Boo”的日常生活是一群只会潜水的壮汉们全裸喝酒，伊织和虽然长得很帅但却是个真正的宅男今村耕平一起，完全被社团所感染。与此同时，结束冲绳的潜水执照讲习，回到伊豆的伊织收到了妹妹栞寄来的信……妹妹来袭、青梅女子大学的学园祭、试胆约会的无人岛露营！被可爱的全裸混蛋们包围的伊织的大学生活再次开始！！(String)
2025-06-29 00:46:53.034 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.P.insertVideoInfo : <==    Updates: 1
2025-06-29 00:46:53.034 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@a7cd1c9]
2025-06-29 00:46:53.034 [34mINFO [0;39m [http-nio-8080-exec-8] c.e.p.s.impl.PureVideoUrlServiceImpl : 插入视频信息成功：碧蓝之海第二季
2025-06-29 00:46:53.038 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@a7cd1c9] from current transaction
2025-06-29 00:46:53.038 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.P.findVideoInfoByTitle : ==>  Preparing: SELECT id, title, cover_image_url, description, created_time, updated_time FROM video_info WHERE title = ? LIMIT 1
2025-06-29 00:46:53.038 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.P.findVideoInfoByTitle : ==> Parameters: 碧蓝之海第二季(String)
2025-06-29 00:46:53.051 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.P.findVideoInfoByTitle : <==      Total: 1
2025-06-29 00:46:53.051 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@a7cd1c9]
2025-06-29 00:46:53.051 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@a7cd1c9] from current transaction
2025-06-29 00:46:53.051 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.P.findVideoTypeByName : ==>  Preparing: SELECT * FROM video_type WHERE name = ?
2025-06-29 00:46:53.051 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.P.findVideoTypeByName : ==> Parameters: 2025(String)
2025-06-29 00:46:53.064 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.P.findVideoTypeByName : <==      Total: 1
2025-06-29 00:46:53.064 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@a7cd1c9]
2025-06-29 00:46:53.064 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@a7cd1c9] from current transaction
2025-06-29 00:46:53.064 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.P.findVideoTypeByName : ==>  Preparing: SELECT * FROM video_type WHERE name = ?
2025-06-29 00:46:53.064 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.P.findVideoTypeByName : ==> Parameters: 日本(String)
2025-06-29 00:46:53.074 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.P.findVideoTypeByName : <==      Total: 1
2025-06-29 00:46:53.074 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@a7cd1c9]
2025-06-29 00:46:53.074 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@a7cd1c9] from current transaction
2025-06-29 00:46:53.074 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.P.findVideoTypeByName : ==>  Preparing: SELECT * FROM video_type WHERE name = ?
2025-06-29 00:46:53.075 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.P.findVideoTypeByName : ==> Parameters: 搞笑(String)
2025-06-29 00:46:53.086 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.P.findVideoTypeByName : <==      Total: 1
2025-06-29 00:46:53.087 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@a7cd1c9]
2025-06-29 00:46:53.087 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@a7cd1c9] from current transaction
2025-06-29 00:46:53.087 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.P.findVideoTypeByName : ==>  Preparing: SELECT * FROM video_type WHERE name = ?
2025-06-29 00:46:53.087 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.P.findVideoTypeByName : ==> Parameters: 校园(String)
2025-06-29 00:46:53.098 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.P.findVideoTypeByName : <==      Total: 1
2025-06-29 00:46:53.099 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@a7cd1c9]
2025-06-29 00:46:53.099 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@a7cd1c9] from current transaction
2025-06-29 00:46:53.099 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.P.findVideoTypeByName : ==>  Preparing: SELECT * FROM video_type WHERE name = ?
2025-06-29 00:46:53.099 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.P.findVideoTypeByName : ==> Parameters: 爱情(String)
2025-06-29 00:46:53.111 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.P.findVideoTypeByName : <==      Total: 1
2025-06-29 00:46:53.111 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@a7cd1c9]
2025-06-29 00:46:53.111 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@a7cd1c9] from current transaction
2025-06-29 00:46:53.112 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.P.findVideoTypeByName : ==>  Preparing: SELECT * FROM video_type WHERE name = ?
2025-06-29 00:46:53.112 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.P.findVideoTypeByName : ==> Parameters: 日本动漫(String)
2025-06-29 00:46:53.124 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.P.findVideoTypeByName : <==      Total: 1
2025-06-29 00:46:53.124 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@a7cd1c9]
2025-06-29 00:46:53.124 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@a7cd1c9] from current transaction
2025-06-29 00:46:53.125 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.P.insertVideoInfoTypeLink : ==>  Preparing: INSERT INTO video_info_type_link( video_info_id, type_id, created_time, updated_time ) VALUES ( ?, ?, NOW(), NOW() ) , ( ?, ?, NOW(), NOW() ) , ( ?, ?, NOW(), NOW() ) , ( ?, ?, NOW(), NOW() ) , ( ?, ?, NOW(), NOW() ) , ( ?, ?, NOW(), NOW() )
2025-06-29 00:46:53.125 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.P.insertVideoInfoTypeLink : ==> Parameters: 80(Long), 36(Long), 80(Long), 30(Long), 80(Long), 48(Long), 80(Long), 42(Long), 80(Long), 45(Long), 80(Long), 35(Long)
2025-06-29 00:46:53.146 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.P.insertVideoInfoTypeLink : <==    Updates: 6
2025-06-29 00:46:53.147 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@a7cd1c9]
2025-06-29 00:46:53.147 [34mINFO [0;39m [http-nio-8080-exec-8] c.e.p.s.impl.PureVideoUrlServiceImpl : 插入视频类型关联信息成功，视频ID: 80
2025-06-29 00:46:53.147 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@a7cd1c9] from current transaction
2025-06-29 00:46:53.147 [34mINFO [0;39m [file-async-2] c.e.p.s.impl.PureVideoUrlServiceImpl : 开始异步下载并上传视频文件 - videoId: 80, episode: 1, url: http://p16-va-tiktok.ibyteimg.com/obj/tos-alisg-v-0051c001-sg/os7ZseD8JAvtGebIgGAIA7KAHMeLIRMjCfe22y
2025-06-29 00:46:53.147 [34mINFO [0;39m [file-async-1] c.e.p.s.impl.PureVideoUrlServiceImpl : 开始异步生成雪碧图 - videoId: 80, episode: 1
2025-06-29 00:46:53.148 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.P.insertVideoEpisodes : ==>  Preparing: INSERT INTO video_episodes( video_info_id, number, object_key, duration, sprite_sheet_object_key, created_time, updated_time ) VALUES ( ?, ?, ?, ?, ?, NOW(), NOW() )
2025-06-29 00:46:53.148 [34mINFO [0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl : Executing command: ffmpeg -i http://p16-va-tiktok.ibyteimg.com/obj/tos-alisg-v-0051c001-sg/os7ZseD8JAvtGebIgGAIA7KAHMeLIRMjCfe22y -vf fps=1/3,scale=160:-1,tile=23x23 -q:v 2 -y d:\upload\video_sprites\80\output_1.jpg
2025-06-29 00:46:53.148 [39mDEBUG[0;39m [file-async-2] o.s.w.r.f.client.ExchangeFunctions : [38dab674] HTTP GET http://p16-va-tiktok.ibyteimg.com/obj/tos-alisg-v-0051c001-sg/os7ZseD8JAvtGebIgGAIA7KAHMeLIRMjCfe22y
2025-06-29 00:46:53.148 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.P.insertVideoEpisodes : ==> Parameters: 80(Long), 1(String), null, null, null
2025-06-29 00:46:53.168 [39mDEBUG[0;39m [http-nio-8080-exec-8] c.e.p.m.p.P.insertVideoEpisodes : <==    Updates: 1
2025-06-29 00:46:53.168 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@a7cd1c9]
2025-06-29 00:46:53.168 [34mINFO [0;39m [http-nio-8080-exec-8] c.e.p.s.impl.PureVideoUrlServiceImpl : 插入视频分集信息成功，共1集，视频ID: 80
2025-06-29 00:46:53.168 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@a7cd1c9]
2025-06-29 00:46:53.168 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@a7cd1c9]
2025-06-29 00:46:53.168 [39mDEBUG[0;39m [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@a7cd1c9]
2025-06-29 00:46:53.191 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : Using @ExceptionHandler com.example.pure.exception.GlobalExceptionHandler#handleException(Exception)
2025-06-29 00:46:53.191 [1;31mERROR[0;39m [http-nio-8080-exec-8] c.e.p.e.GlobalExceptionHandler : 服务器处理API出现异常
java.lang.IllegalArgumentException: Cache 'videoInfo' does not allow 'null' values. Avoid storing null via '@Cacheable(unless="#result == null")' or configure RedisCache to allow 'null' via RedisCacheConfiguration.
	at org.springframework.data.redis.cache.RedisCache.put(RedisCache.java:166)
	at org.springframework.cache.transaction.TransactionAwareCacheDecorator$1.afterCommit(TransactionAwareCacheDecorator.java:100)
	at org.springframework.transaction.support.TransactionSynchronizationUtils.invokeAfterCommit(TransactionSynchronizationUtils.java:135)
	at org.springframework.transaction.support.TransactionSynchronizationUtils.triggerAfterCommit(TransactionSynchronizationUtils.java:123)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.triggerAfterCommit(AbstractPlatformTransactionManager.java:936)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.processCommit(AbstractPlatformTransactionManager.java:782)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.commit(AbstractPlatformTransactionManager.java:711)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.commitTransactionAfterReturning(TransactionAspectSupport.java:654)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:407)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.service.impl.PureVideoUrlServiceImpl$$EnhancerBySpringCGLIB$$7754b21f.uploadVideoWithEpisodes(<generated>)
	at com.example.pure.controller.PureVideoUrlController.uploadVideoWithEpisodes(PureVideoUrlController.java:42)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-06-29 00:46:53.192 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-29 00:46:53.192 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Writing [Result(code=500, message=服务器处理API出现异常，请联系管理员, success=false, data=null, time=2025-06-28T16:46:53.192 (truncated)...]
2025-06-29 00:46:53.193 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : Resolved [java.lang.IllegalArgumentException: Cache 'videoInfo' does not allow 'null' values. Avoid storing null via '@Cacheable(unless="#result == null")' or configure RedisCache to allow 'null' via RedisCacheConfiguration.]
2025-06-29 00:46:53.193 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.web.servlet.DispatcherServlet : Completed 500 INTERNAL_SERVER_ERROR
2025-06-29 00:46:53.193 [39mDEBUG[0;39m [http-nio-8080-exec-8] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-29 00:46:53.205 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl : ffmpeg version 7.1.1-full_build-www.gyan.dev Copyright (c) 2000-2025 the FFmpeg developers
2025-06-29 00:46:53.205 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :   built with gcc 14.2.0 (Rev1, Built by MSYS2 project)
2025-06-29 00:46:53.205 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :   configuration: --enable-gpl --enable-version3 --enable-static --disable-w32threads --disable-autodetect --enable-fontconfig --enable-iconv --enable-gnutls --enable-lcms2 --enable-libxml2 --enable-gmp --enable-bzlib --enable-lzma --enable-libsnappy --enable-zlib --enable-librist --enable-libsrt --enable-libssh --enable-libzmq --enable-avisynth --enable-libbluray --enable-libcaca --enable-libdvdnav --enable-libdvdread --enable-sdl2 --enable-libaribb24 --enable-libaribcaption --enable-libdav1d --enable-libdavs2 --enable-libopenjpeg --enable-libquirc --enable-libuavs3d --enable-libxevd --enable-libzvbi --enable-libqrencode --enable-librav1e --enable-libsvtav1 --enable-libvvenc --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxavs2 --enable-libxeve --enable-libxvid --enable-libaom --enable-libjxl --enable-libvpx --enable-mediafoundation --enable-libass --enable-frei0r --enable-libfreetype --enable-libfribidi --enable-libharfbuzz --enable-liblensfun --enable-libvidstab --enable-libvmaf --enable-libzimg --enable-amf --enable-cuda-llvm --enable-cuvid --enable-dxva2 --enable-d3d11va --enable-d3d12va --enable-ffnvcodec --enable-libvpl --enable-nvdec --enable-nvenc --enable-vaapi --enable-libshaderc --enable-vulkan --enable-libplacebo --enable-opencl --enable-libcdio --enable-libgme --enable-libmodplug --enable-libopenmpt --enable-libopencore-amrwb --enable-libmp3lame --enable-libshine --enable-libtheora --enable-libtwolame --enable-libvo-amrwbenc --enable-libcodec2 --enable-libilbc --enable-libgsm --enable-liblc3 --enable-libopencore-amrnb --enable-libopus --enable-libspeex --enable-libvorbis --enable-ladspa --enable-libbs2b --enable-libflite --enable-libmysofa --enable-librubberband --enable-libsoxr --enable-chromaprint
2025-06-29 00:46:53.205 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :   libavutil      59. 39.100 / 59. 39.100
2025-06-29 00:46:53.205 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :   libavcodec     61. 19.101 / 61. 19.101
2025-06-29 00:46:53.205 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :   libavformat    61.  7.100 / 61.  7.100
2025-06-29 00:46:53.205 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :   libavdevice    61.  3.100 / 61.  3.100
2025-06-29 00:46:53.205 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :   libavfilter    10.  4.100 / 10.  4.100
2025-06-29 00:46:53.205 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :   libswscale      8.  3.100 /  8.  3.100
2025-06-29 00:46:53.205 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :   libswresample   5.  3.100 /  5.  3.100
2025-06-29 00:46:53.205 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :   libpostproc    58.  3.100 / 58.  3.100
2025-06-29 00:46:53.882 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl : Input #0, mov,mp4,m4a,3gp,3g2,mj2, from 'http://p16-va-tiktok.ibyteimg.com/obj/tos-alisg-v-0051c001-sg/os7ZseD8JAvtGebIgGAIA7KAHMeLIRMjCfe22y':
2025-06-29 00:46:53.882 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :   Metadata:
2025-06-29 00:46:53.882 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :     major_brand     : isom
2025-06-29 00:46:53.882 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :     minor_version   : 512
2025-06-29 00:46:53.882 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :     compatible_brands: isomiso2avc1mp41
2025-06-29 00:46:53.882 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :     encoder         : Lavf57.83.100
2025-06-29 00:46:53.882 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :     description     : Packed by Bilibili XCoder v2.0.2
2025-06-29 00:46:53.882 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :   Duration: 00:01:14.54, start: 0.000000, bitrate: 1567 kb/s
2025-06-29 00:46:53.882 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :   Stream #0:0[0x1](und): Video: h264 (High) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 1920x1080 [SAR 1:1 DAR 16:9], 1464 kb/s, 23.96 fps, 23.98 tbr, 16k tbn (default)
2025-06-29 00:46:53.882 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :       Metadata:
2025-06-29 00:46:53.882 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :         handler_name    : VideoHandler
2025-06-29 00:46:53.882 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :         vendor_id       : [0][0][0][0]
2025-06-29 00:46:53.882 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :   Stream #0:1[0x2](und): Audio: aac (LC) (mp4a / 0x6134706D), 44100 Hz, stereo, fltp, 99 kb/s (default)
2025-06-29 00:46:53.882 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :       Metadata:
2025-06-29 00:46:53.882 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :         handler_name    : SoundHandler
2025-06-29 00:46:53.882 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :         vendor_id       : [0][0][0][0]
2025-06-29 00:46:53.888 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl : Stream mapping:
2025-06-29 00:46:53.888 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :   Stream #0:0 -> #0:0 (h264 (native) -> mjpeg (native))
2025-06-29 00:46:53.888 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl : Press [q] to stop, [?] for help
2025-06-29 00:46:56.449 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl : Output #0, image2, to 'd:\upload\video_sprites\80\output_1.jpg':
2025-06-29 00:46:56.449 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :   Metadata:
2025-06-29 00:46:56.449 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :     major_brand     : isom
2025-06-29 00:46:56.449 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :     minor_version   : 512
2025-06-29 00:46:56.449 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :     compatible_brands: isomiso2avc1mp41
2025-06-29 00:46:56.449 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :     description     : Packed by Bilibili XCoder v2.0.2
2025-06-29 00:46:56.449 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :     encoder         : Lavf61.7.100
2025-06-29 00:46:56.449 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :   Stream #0:0(und): Video: mjpeg, yuv420p(pc, bt709, progressive), 3680x2070 [SAR 1:1 DAR 16:9], q=2-31, 200 kb/s, 0.0006 fps, 0.0006 tbn (default)
2025-06-29 00:46:56.449 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :       Metadata:
2025-06-29 00:46:56.449 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :         handler_name    : VideoHandler
2025-06-29 00:46:56.449 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :         vendor_id       : [0][0][0][0]
2025-06-29 00:46:56.449 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :         encoder         : Lavc61.19.101 mjpeg
2025-06-29 00:46:56.449 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :       Side data:
2025-06-29 00:46:56.449 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :         cpb: bitrate max/min/avg: 0/0/200000 buffer size: 0 vbv_delay: N/A
2025-06-29 00:46:56.466 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl : frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A speed=N/A    
2025-06-29 00:46:56.470 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl : [image2 @ 000002f553225880] The specified filename 'd:\upload\video_sprites\80\output_1.jpg' does not contain an image sequence pattern or a pattern is invalid.
2025-06-29 00:46:56.470 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl : [image2 @ 000002f553225880] Use a pattern such as %03d for an image sequence or use the -update option (with -frames:v 1 if needed) to write a single image.
2025-06-29 00:46:56.471 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl : [out#0/image2 @ 000002f553215580] video:218KiB audio:0KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing overhead: unknown
2025-06-29 00:46:56.471 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl : frame=    1 fps=0.4 q=2.0 Lsize=N/A time=00:26:27.00 bitrate=N/A speed= 614x    
2025-06-29 00:46:56.496 [34mINFO [0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl : 命令成功执行。
2025-06-29 00:46:58.298 [39mDEBUG[0;39m [reactor-http-nio-3] o.s.w.r.f.client.ExchangeFunctions : [38dab674] [00a8eab8-1] Response 200 OK
2025-06-29 00:46:59.189 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.security.web.FilterChainProxy : Securing POST /api/videoUrl/video-episodes
2025-06-29 00:46:59.189 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-29 00:46:59.189 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#uploadVideo(VideoInfoWithEpisodesDto)
2025-06-29 00:46:59.189 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.s.w.a.AnonymousAuthenticationFilter : Set SecurityContextHolder to anonymous SecurityContext
2025-06-29 00:46:59.190 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /api/videoUrl/video-episodes] with attributes [permitAll]
2025-06-29 00:46:59.190 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.security.web.FilterChainProxy : Secured POST /api/videoUrl/video-episodes
2025-06-29 00:46:59.190 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.web.servlet.DispatcherServlet : POST "/api/videoUrl/video-episodes", parameters={}
2025-06-29 00:46:59.190 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#uploadVideo(VideoInfoWithEpisodesDto)
2025-06-29 00:46:59.191 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [VideoInfoWithEpisodesDto(videoEpisodes=[VideoEpisodesDTO(id=null, videoInfoId=null, number=1, object (truncated)...]
2025-06-29 00:46:59.214 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-29 00:46:59.214 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6b99e8dc]
2025-06-29 00:46:59.214 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1633780652 wrapping com.mysql.cj.jdbc.ConnectionImpl@78b35d7b] will be managed by Spring
2025-06-29 00:46:59.214 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.P.findVideoInfoByTitle : ==>  Preparing: SELECT id, title, cover_image_url, description, created_time, updated_time FROM video_info WHERE title = ? LIMIT 1
2025-06-29 00:46:59.215 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.P.findVideoInfoByTitle : ==> Parameters: ChaO(String)
2025-06-29 00:46:59.227 [39mDEBUG[0;39m [http-nio-8080-exec-9] c.e.p.m.p.P.findVideoInfoByTitle : <==      Total: 0
2025-06-29 00:46:59.227 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6b99e8dc]
2025-06-29 00:46:59.227 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6b99e8dc]
2025-06-29 00:46:59.227 [39mDEBUG[0;39m [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6b99e8dc]
2025-06-29 00:46:59.249 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : Using @ExceptionHandler com.example.pure.exception.GlobalExceptionHandler#handleBusinessException(BusinessException)
2025-06-29 00:46:59.249 [31mWARN [0;39m [http-nio-8080-exec-9] c.e.p.e.GlobalExceptionHandler : 业务异常: 查找不到视频信息
2025-06-29 00:46:59.250 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-29 00:46:59.250 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=600, message=查找不到视频信息, success=false, data=null, time=2025-06-28T16:46:59.249500500Z)]
2025-06-29 00:46:59.251 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : Resolved [com.example.pure.exception.BusinessException: 查找不到视频信息]
2025-06-29 00:46:59.251 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.web.servlet.DispatcherServlet : Completed 400 BAD_REQUEST
2025-06-29 00:46:59.251 [39mDEBUG[0;39m [http-nio-8080-exec-9] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-29 00:46:59.308 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.security.web.FilterChainProxy : Securing POST /api/videoUrl/videoinfo-with-episodes
2025-06-29 00:46:59.308 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-29 00:46:59.308 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#uploadVideoWithEpisodes(VideoInfoWithEpisodesDto)
2025-06-29 00:46:59.308 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.s.w.a.AnonymousAuthenticationFilter : Set SecurityContextHolder to anonymous SecurityContext
2025-06-29 00:46:59.309 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /api/videoUrl/videoinfo-with-episodes] with attributes [permitAll]
2025-06-29 00:46:59.309 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.security.web.FilterChainProxy : Secured POST /api/videoUrl/videoinfo-with-episodes
2025-06-29 00:46:59.309 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.web.servlet.DispatcherServlet : POST "/api/videoUrl/videoinfo-with-episodes", parameters={}
2025-06-29 00:46:59.310 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#uploadVideoWithEpisodes(VideoInfoWithEpisodesDto)
2025-06-29 00:46:59.311 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [VideoInfoWithEpisodesDto(videoEpisodes=[VideoEpisodesDTO(id=null, videoInfoId=null, number=1, object (truncated)...]
2025-06-29 00:46:59.325 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-29 00:46:59.325 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f9c90f6]
2025-06-29 00:46:59.325 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@358939526 wrapping com.mysql.cj.jdbc.ConnectionImpl@78b35d7b] will be managed by Spring
2025-06-29 00:46:59.325 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.P.findVideoInfoByTitle : ==>  Preparing: SELECT id, title, cover_image_url, description, created_time, updated_time FROM video_info WHERE title = ? LIMIT 1
2025-06-29 00:46:59.326 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.P.findVideoInfoByTitle : ==> Parameters: ChaO(String)
2025-06-29 00:46:59.337 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.P.findVideoInfoByTitle : <==      Total: 0
2025-06-29 00:46:59.338 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f9c90f6]
2025-06-29 00:46:59.338 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f9c90f6] from current transaction
2025-06-29 00:46:59.338 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.P.insertVideoInfo : ==>  Preparing: INSERT INTO video_info( title, cover_image_url, description, created_time, updated_time ) VALUES ( ?, ?, ?, NOW(), NOW() )
2025-06-29 00:46:59.339 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.P.insertVideoInfo : ==> Parameters: ChaO(String), https://lain.bgm.tv/r/400/pic/cover/l/06/8a/533246_20WBZ.jpg(String), 上班族 Stefan 突然被美人鱼王国的公主 Chao 追求....... 问题是，不同种族和文化的相互理解需要什么。(String)
2025-06-29 00:46:59.359 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.P.insertVideoInfo : <==    Updates: 1
2025-06-29 00:46:59.360 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f9c90f6]
2025-06-29 00:46:59.360 [34mINFO [0;39m [http-nio-8080-exec-10] c.e.p.s.impl.PureVideoUrlServiceImpl : 插入视频信息成功：ChaO
2025-06-29 00:46:59.363 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f9c90f6] from current transaction
2025-06-29 00:46:59.364 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.P.findVideoInfoByTitle : ==>  Preparing: SELECT id, title, cover_image_url, description, created_time, updated_time FROM video_info WHERE title = ? LIMIT 1
2025-06-29 00:46:59.364 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.P.findVideoInfoByTitle : ==> Parameters: ChaO(String)
2025-06-29 00:46:59.374 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.P.findVideoInfoByTitle : <==      Total: 1
2025-06-29 00:46:59.375 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f9c90f6]
2025-06-29 00:46:59.375 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f9c90f6] from current transaction
2025-06-29 00:46:59.375 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.P.findVideoTypeByName : ==>  Preparing: SELECT * FROM video_type WHERE name = ?
2025-06-29 00:46:59.375 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.P.findVideoTypeByName : ==> Parameters: 2025(String)
2025-06-29 00:46:59.386 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.P.findVideoTypeByName : <==      Total: 1
2025-06-29 00:46:59.386 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f9c90f6]
2025-06-29 00:46:59.386 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f9c90f6] from current transaction
2025-06-29 00:46:59.387 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.P.findVideoTypeByName : ==>  Preparing: SELECT * FROM video_type WHERE name = ?
2025-06-29 00:46:59.387 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.P.findVideoTypeByName : ==> Parameters: 日本(String)
2025-06-29 00:46:59.398 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.P.findVideoTypeByName : <==      Total: 1
2025-06-29 00:46:59.398 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f9c90f6]
2025-06-29 00:46:59.398 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f9c90f6] from current transaction
2025-06-29 00:46:59.398 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.P.findVideoTypeByName : ==>  Preparing: SELECT * FROM video_type WHERE name = ?
2025-06-29 00:46:59.399 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.P.findVideoTypeByName : ==> Parameters: 爱情(String)
2025-06-29 00:46:59.410 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.P.findVideoTypeByName : <==      Total: 1
2025-06-29 00:46:59.411 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f9c90f6]
2025-06-29 00:46:59.411 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f9c90f6] from current transaction
2025-06-29 00:46:59.411 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.P.findVideoTypeByName : ==>  Preparing: SELECT * FROM video_type WHERE name = ?
2025-06-29 00:46:59.411 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.P.findVideoTypeByName : ==> Parameters: 奇幻(String)
2025-06-29 00:46:59.422 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.P.findVideoTypeByName : <==      Total: 1
2025-06-29 00:46:59.424 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f9c90f6]
2025-06-29 00:46:59.424 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f9c90f6] from current transaction
2025-06-29 00:46:59.424 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.P.findVideoTypeByName : ==>  Preparing: SELECT * FROM video_type WHERE name = ?
2025-06-29 00:46:59.424 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.P.findVideoTypeByName : ==> Parameters: 日本动漫(String)
2025-06-29 00:46:59.434 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.P.findVideoTypeByName : <==      Total: 1
2025-06-29 00:46:59.434 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f9c90f6]
2025-06-29 00:46:59.434 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f9c90f6] from current transaction
2025-06-29 00:46:59.435 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.P.insertVideoInfoTypeLink : ==>  Preparing: INSERT INTO video_info_type_link( video_info_id, type_id, created_time, updated_time ) VALUES ( ?, ?, NOW(), NOW() ) , ( ?, ?, NOW(), NOW() ) , ( ?, ?, NOW(), NOW() ) , ( ?, ?, NOW(), NOW() ) , ( ?, ?, NOW(), NOW() )
2025-06-29 00:46:59.435 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.P.insertVideoInfoTypeLink : ==> Parameters: 81(Long), 36(Long), 81(Long), 30(Long), 81(Long), 45(Long), 81(Long), 32(Long), 81(Long), 35(Long)
2025-06-29 00:46:59.457 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.P.insertVideoInfoTypeLink : <==    Updates: 5
2025-06-29 00:46:59.458 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f9c90f6]
2025-06-29 00:46:59.458 [34mINFO [0;39m [http-nio-8080-exec-10] c.e.p.s.impl.PureVideoUrlServiceImpl : 插入视频类型关联信息成功，视频ID: 81
2025-06-29 00:46:59.458 [34mINFO [0;39m [file-async-4] c.e.p.s.impl.PureVideoUrlServiceImpl : 开始异步下载并上传视频文件 - videoId: 81, episode: 1, url: http://p16-va-tiktok.ibyteimg.com/obj/tos-alisg-v-0051c001-sg/oM3vGeaRGIZgIuDUegQALCHeIlbBAQtmaDiZJr
2025-06-29 00:46:59.458 [34mINFO [0;39m [file-async-5] c.e.p.s.impl.PureVideoUrlServiceImpl : 开始异步生成雪碧图 - videoId: 81, episode: 1
2025-06-29 00:46:59.458 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f9c90f6] from current transaction
2025-06-29 00:46:59.459 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.P.insertVideoEpisodes : ==>  Preparing: INSERT INTO video_episodes( video_info_id, number, object_key, duration, sprite_sheet_object_key, created_time, updated_time ) VALUES ( ?, ?, ?, ?, ?, NOW(), NOW() )
2025-06-29 00:46:59.459 [39mDEBUG[0;39m [file-async-4] o.s.w.r.f.client.ExchangeFunctions : [7a5eac85] HTTP GET http://p16-va-tiktok.ibyteimg.com/obj/tos-alisg-v-0051c001-sg/oM3vGeaRGIZgIuDUegQALCHeIlbBAQtmaDiZJr
2025-06-29 00:46:59.459 [34mINFO [0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl : Executing command: ffmpeg -i http://p16-va-tiktok.ibyteimg.com/obj/tos-alisg-v-0051c001-sg/oM3vGeaRGIZgIuDUegQALCHeIlbBAQtmaDiZJr -vf fps=1/3,scale=160:-1,tile=23x23 -q:v 2 -y d:\upload\video_sprites\81\output_1.jpg
2025-06-29 00:46:59.459 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.P.insertVideoEpisodes : ==> Parameters: 81(Long), 1(String), null, null, null
2025-06-29 00:46:59.480 [39mDEBUG[0;39m [http-nio-8080-exec-10] c.e.p.m.p.P.insertVideoEpisodes : <==    Updates: 1
2025-06-29 00:46:59.480 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f9c90f6]
2025-06-29 00:46:59.480 [34mINFO [0;39m [http-nio-8080-exec-10] c.e.p.s.impl.PureVideoUrlServiceImpl : 插入视频分集信息成功，共1集，视频ID: 81
2025-06-29 00:46:59.480 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f9c90f6]
2025-06-29 00:46:59.480 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f9c90f6]
2025-06-29 00:46:59.481 [39mDEBUG[0;39m [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f9c90f6]
2025-06-29 00:46:59.506 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : Using @ExceptionHandler com.example.pure.exception.GlobalExceptionHandler#handleException(Exception)
2025-06-29 00:46:59.507 [1;31mERROR[0;39m [http-nio-8080-exec-10] c.e.p.e.GlobalExceptionHandler : 服务器处理API出现异常
java.lang.IllegalArgumentException: Cache 'videoInfo' does not allow 'null' values. Avoid storing null via '@Cacheable(unless="#result == null")' or configure RedisCache to allow 'null' via RedisCacheConfiguration.
	at org.springframework.data.redis.cache.RedisCache.put(RedisCache.java:166)
	at org.springframework.cache.transaction.TransactionAwareCacheDecorator$1.afterCommit(TransactionAwareCacheDecorator.java:100)
	at org.springframework.transaction.support.TransactionSynchronizationUtils.invokeAfterCommit(TransactionSynchronizationUtils.java:135)
	at org.springframework.transaction.support.TransactionSynchronizationUtils.triggerAfterCommit(TransactionSynchronizationUtils.java:123)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.triggerAfterCommit(AbstractPlatformTransactionManager.java:936)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.processCommit(AbstractPlatformTransactionManager.java:782)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.commit(AbstractPlatformTransactionManager.java:711)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.commitTransactionAfterReturning(TransactionAspectSupport.java:654)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:407)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.service.impl.PureVideoUrlServiceImpl$$EnhancerBySpringCGLIB$$7754b21f.uploadVideoWithEpisodes(<generated>)
	at com.example.pure.controller.PureVideoUrlController.uploadVideoWithEpisodes(PureVideoUrlController.java:42)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-06-29 00:46:59.508 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-29 00:46:59.508 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.w.s.m.m.a.HttpEntityMethodProcessor : Writing [Result(code=500, message=服务器处理API出现异常，请联系管理员, success=false, data=null, time=2025-06-28T16:46:59.507 (truncated)...]
2025-06-29 00:46:59.509 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl : ffmpeg version 7.1.1-full_build-www.gyan.dev Copyright (c) 2000-2025 the FFmpeg developers
2025-06-29 00:46:59.509 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :   built with gcc 14.2.0 (Rev1, Built by MSYS2 project)
2025-06-29 00:46:59.509 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :   configuration: --enable-gpl --enable-version3 --enable-static --disable-w32threads --disable-autodetect --enable-fontconfig --enable-iconv --enable-gnutls --enable-lcms2 --enable-libxml2 --enable-gmp --enable-bzlib --enable-lzma --enable-libsnappy --enable-zlib --enable-librist --enable-libsrt --enable-libssh --enable-libzmq --enable-avisynth --enable-libbluray --enable-libcaca --enable-libdvdnav --enable-libdvdread --enable-sdl2 --enable-libaribb24 --enable-libaribcaption --enable-libdav1d --enable-libdavs2 --enable-libopenjpeg --enable-libquirc --enable-libuavs3d --enable-libxevd --enable-libzvbi --enable-libqrencode --enable-librav1e --enable-libsvtav1 --enable-libvvenc --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxavs2 --enable-libxeve --enable-libxvid --enable-libaom --enable-libjxl --enable-libvpx --enable-mediafoundation --enable-libass --enable-frei0r --enable-libfreetype --enable-libfribidi --enable-libharfbuzz --enable-liblensfun --enable-libvidstab --enable-libvmaf --enable-libzimg --enable-amf --enable-cuda-llvm --enable-cuvid --enable-dxva2 --enable-d3d11va --enable-d3d12va --enable-ffnvcodec --enable-libvpl --enable-nvdec --enable-nvenc --enable-vaapi --enable-libshaderc --enable-vulkan --enable-libplacebo --enable-opencl --enable-libcdio --enable-libgme --enable-libmodplug --enable-libopenmpt --enable-libopencore-amrwb --enable-libmp3lame --enable-libshine --enable-libtheora --enable-libtwolame --enable-libvo-amrwbenc --enable-libcodec2 --enable-libilbc --enable-libgsm --enable-liblc3 --enable-libopencore-amrnb --enable-libopus --enable-libspeex --enable-libvorbis --enable-ladspa --enable-libbs2b --enable-libflite --enable-libmysofa --enable-librubberband --enable-libsoxr --enable-chromaprint
2025-06-29 00:46:59.509 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :   libavutil      59. 39.100 / 59. 39.100
2025-06-29 00:46:59.509 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :   libavcodec     61. 19.101 / 61. 19.101
2025-06-29 00:46:59.509 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :   libavformat    61.  7.100 / 61.  7.100
2025-06-29 00:46:59.509 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :   libavdevice    61.  3.100 / 61.  3.100
2025-06-29 00:46:59.509 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :   libavfilter    10.  4.100 / 10.  4.100
2025-06-29 00:46:59.509 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :   libswscale      8.  3.100 /  8.  3.100
2025-06-29 00:46:59.509 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :   libswresample   5.  3.100 /  5.  3.100
2025-06-29 00:46:59.509 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :   libpostproc    58.  3.100 / 58.  3.100
2025-06-29 00:46:59.509 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : Resolved [java.lang.IllegalArgumentException: Cache 'videoInfo' does not allow 'null' values. Avoid storing null via '@Cacheable(unless="#result == null")' or configure RedisCache to allow 'null' via RedisCacheConfiguration.]
2025-06-29 00:46:59.509 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.web.servlet.DispatcherServlet : Completed 500 INTERNAL_SERVER_ERROR
2025-06-29 00:46:59.509 [39mDEBUG[0;39m [http-nio-8080-exec-10] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-29 00:46:59.804 [39mDEBUG[0;39m [reactor-http-nio-3] org.springframework.web.HttpLogging : [38dab674] [00a8eab8-1] Read 14605260 bytes
2025-06-29 00:46:59.804 [34mINFO [0;39m [file-async-2] c.e.p.s.impl.PureVideoUrlServiceImpl : 视频文件下载完成 - videoId: 80, episode: 1, 文件大小: 14605260 bytes
2025-06-29 00:46:59.895 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl : Input #0, mov,mp4,m4a,3gp,3g2,mj2, from 'http://p16-va-tiktok.ibyteimg.com/obj/tos-alisg-v-0051c001-sg/oM3vGeaRGIZgIuDUegQALCHeIlbBAQtmaDiZJr':
2025-06-29 00:46:59.896 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :   Metadata:
2025-06-29 00:46:59.896 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :     major_brand     : isom
2025-06-29 00:46:59.896 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :     minor_version   : 512
2025-06-29 00:46:59.896 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :     compatible_brands: isomiso2avc1mp41
2025-06-29 00:46:59.896 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :     encoder         : Lavf57.83.100
2025-06-29 00:46:59.896 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :     description     : Packed by Bilibili XCoder v2.0.2
2025-06-29 00:46:59.896 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :   Duration: 00:01:00.07, start: 0.000000, bitrate: 1763 kb/s
2025-06-29 00:46:59.896 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :   Stream #0:0[0x1](und): Video: h264 (High) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 1920x1080 [SAR 1:1 DAR 16:9], 1669 kb/s, 24 fps, 24 tbr, 16k tbn (default)
2025-06-29 00:46:59.896 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :       Metadata:
2025-06-29 00:46:59.896 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :         handler_name    : VideoHandler
2025-06-29 00:46:59.896 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :         vendor_id       : [0][0][0][0]
2025-06-29 00:46:59.896 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :   Stream #0:1[0x2](und): Audio: aac (LC) (mp4a / 0x6134706D), 44100 Hz, stereo, fltp, 88 kb/s (default)
2025-06-29 00:46:59.896 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :       Metadata:
2025-06-29 00:46:59.896 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :         handler_name    : SoundHandler
2025-06-29 00:46:59.896 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :         vendor_id       : [0][0][0][0]
2025-06-29 00:46:59.900 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl : Stream mapping:
2025-06-29 00:46:59.900 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :   Stream #0:0 -> #0:0 (h264 (native) -> mjpeg (native))
2025-06-29 00:46:59.900 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl : Press [q] to stop, [?] for help
2025-06-29 00:47:00.260 [39mDEBUG[0;39m [file-async-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-29 00:47:00.260 [39mDEBUG[0;39m [file-async-1] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7c86fc34] was not registered for synchronization because synchronization is not active
2025-06-29 00:47:00.270 [39mDEBUG[0;39m [file-async-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@397202217 wrapping com.mysql.cj.jdbc.ConnectionImpl@78b35d7b] will not be managed by Spring
2025-06-29 00:47:00.270 [39mDEBUG[0;39m [file-async-1] c.e.p.m.p.P.updateSpriteSheetObjectKey : ==>  Preparing: UPDATE video_episodes SET sprite_sheet_object_key = ?, updated_time = NOW() WHERE video_info_id = ? AND number = ?
2025-06-29 00:47:00.271 [39mDEBUG[0;39m [file-async-1] c.e.p.m.p.P.updateSpriteSheetObjectKey : ==> Parameters: video/3690bfd8-4727-4803-b60a-80e7cc46fbf2/1.jpg(String), 80(Long), 1(String)
2025-06-29 00:47:00.294 [39mDEBUG[0;39m [file-async-1] c.e.p.m.p.P.updateSpriteSheetObjectKey : <==    Updates: 1
2025-06-29 00:47:00.294 [39mDEBUG[0;39m [file-async-1] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7c86fc34]
2025-06-29 00:47:00.294 [34mINFO [0;39m [file-async-1] c.e.p.s.impl.PureVideoUrlServiceImpl : 异步生成雪碧图成功并已更新数据库 - videoId: 80, episode: 1, objectKey: video/3690bfd8-4727-4803-b60a-80e7cc46fbf2/1.jpg
2025-06-29 00:47:03.186 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl : Output #0, image2, to 'd:\upload\video_sprites\81\output_1.jpg':
2025-06-29 00:47:03.186 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :   Metadata:
2025-06-29 00:47:03.186 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :     major_brand     : isom
2025-06-29 00:47:03.186 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :     minor_version   : 512
2025-06-29 00:47:03.186 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :     compatible_brands: isomiso2avc1mp41
2025-06-29 00:47:03.186 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :     description     : Packed by Bilibili XCoder v2.0.2
2025-06-29 00:47:03.186 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :     encoder         : Lavf61.7.100
2025-06-29 00:47:03.186 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :   Stream #0:0(und): Video: mjpeg, yuv420p(pc, bt709, progressive), 3680x2070 [SAR 1:1 DAR 16:9], q=2-31, 200 kb/s, 0.0006 fps, 0.0006 tbn (default)
2025-06-29 00:47:03.186 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :       Metadata:
2025-06-29 00:47:03.186 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :         handler_name    : VideoHandler
2025-06-29 00:47:03.186 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :         vendor_id       : [0][0][0][0]
2025-06-29 00:47:03.186 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :         encoder         : Lavc61.19.101 mjpeg
2025-06-29 00:47:03.186 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :       Side data:
2025-06-29 00:47:03.186 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :         cpb: bitrate max/min/avg: 0/0/200000 buffer size: 0 vbv_delay: N/A
2025-06-29 00:47:03.207 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl : [image2 @ 0000022a539e1cc0] The specified filename 'd:\upload\video_sprites\81\output_1.jpg' does not contain an image sequence pattern or a pattern is invalid.
2025-06-29 00:47:03.208 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl : [image2 @ 0000022a539e1cc0] Use a pattern such as %03d for an image sequence or use the -update option (with -frames:v 1 if needed) to write a single image.
2025-06-29 00:47:03.227 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl : [out#0/image2 @ 0000022a539db9c0] video:166KiB audio:0KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing overhead: unknown
2025-06-29 00:47:03.227 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl : frame=    1 fps=0.3 q=2.0 Lsize=N/A time=00:26:27.00 bitrate=N/A speed= 477x    
2025-06-29 00:47:03.253 [34mINFO [0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl : 命令成功执行。
2025-06-29 00:47:04.604 [39mDEBUG[0;39m [reactor-http-nio-4] o.s.w.r.f.client.ExchangeFunctions : [7a5eac85] [c375baaf-1] Response 200 OK
2025-06-29 00:47:04.644 [34mINFO [0;39m [file-async-2] c.e.p.s.impl.PureVideoUrlServiceImpl : 视频文件上传到R2成功 - videoId: 80, episode: 1, objectKey: video/3690bfd8-4727-4803-b60a-80e7cc46fbf2/1.mp4
2025-06-29 00:47:04.644 [39mDEBUG[0;39m [file-async-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-29 00:47:04.644 [39mDEBUG[0;39m [file-async-2] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@27fa3552] was not registered for synchronization because synchronization is not active
2025-06-29 00:47:04.655 [39mDEBUG[0;39m [file-async-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1736005172 wrapping com.mysql.cj.jdbc.ConnectionImpl@78b35d7b] will not be managed by Spring
2025-06-29 00:47:04.655 [39mDEBUG[0;39m [file-async-2] c.e.p.m.p.P.updateEpisodeObjectKey : ==>  Preparing: UPDATE video_episodes SET object_key = ?, updated_time = NOW() WHERE video_info_id = ? AND number = ?
2025-06-29 00:47:04.655 [39mDEBUG[0;39m [file-async-2] c.e.p.m.p.P.updateEpisodeObjectKey : ==> Parameters: video/3690bfd8-4727-4803-b60a-80e7cc46fbf2/1.mp4(String), 80(Long), 1(String)
2025-06-29 00:47:04.678 [39mDEBUG[0;39m [file-async-2] c.e.p.m.p.P.updateEpisodeObjectKey : <==    Updates: 1
2025-06-29 00:47:04.679 [39mDEBUG[0;39m [file-async-2] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@27fa3552]
2025-06-29 00:47:04.679 [34mINFO [0;39m [file-async-2] c.e.p.s.impl.PureVideoUrlServiceImpl : 异步更新数据库objectKey成功 - videoId: 80, episode: 1, objectKey: video/3690bfd8-4727-4803-b60a-80e7cc46fbf2/1.mp4
2025-06-29 00:47:05.124 [39mDEBUG[0;39m [file-async-5] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-29 00:47:05.124 [39mDEBUG[0;39m [file-async-5] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@243556a9] was not registered for synchronization because synchronization is not active
2025-06-29 00:47:05.125 [39mDEBUG[0;39m [file-async-5] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1955083735 wrapping com.mysql.cj.jdbc.ConnectionImpl@78b35d7b] will not be managed by Spring
2025-06-29 00:47:05.125 [39mDEBUG[0;39m [file-async-5] c.e.p.m.p.P.updateSpriteSheetObjectKey : ==>  Preparing: UPDATE video_episodes SET sprite_sheet_object_key = ?, updated_time = NOW() WHERE video_info_id = ? AND number = ?
2025-06-29 00:47:05.125 [39mDEBUG[0;39m [file-async-5] c.e.p.m.p.P.updateSpriteSheetObjectKey : ==> Parameters: video/fc2d0d36-6137-46dd-9d59-e63339e9896f/1.jpg(String), 81(Long), 1(String)
2025-06-29 00:47:05.150 [39mDEBUG[0;39m [file-async-5] c.e.p.m.p.P.updateSpriteSheetObjectKey : <==    Updates: 1
2025-06-29 00:47:05.150 [39mDEBUG[0;39m [file-async-5] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@243556a9]
2025-06-29 00:47:05.150 [34mINFO [0;39m [file-async-5] c.e.p.s.impl.PureVideoUrlServiceImpl : 异步生成雪碧图成功并已更新数据库 - videoId: 81, episode: 1, objectKey: video/fc2d0d36-6137-46dd-9d59-e63339e9896f/1.jpg
2025-06-29 00:47:06.560 [39mDEBUG[0;39m [reactor-http-nio-4] org.springframework.web.HttpLogging : [7a5eac85] [c375baaf-1] Read 13241348 bytes
2025-06-29 00:47:06.560 [34mINFO [0;39m [file-async-4] c.e.p.s.impl.PureVideoUrlServiceImpl : 视频文件下载完成 - videoId: 81, episode: 1, 文件大小: 13241348 bytes
2025-06-29 00:47:09.533 [34mINFO [0;39m [file-async-4] c.e.p.s.impl.PureVideoUrlServiceImpl : 视频文件上传到R2成功 - videoId: 81, episode: 1, objectKey: video/fc2d0d36-6137-46dd-9d59-e63339e9896f/1.mp4
2025-06-29 00:47:09.533 [39mDEBUG[0;39m [file-async-4] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-29 00:47:09.533 [39mDEBUG[0;39m [file-async-4] org.mybatis.spring.SqlSessionUtils : SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1e26f7a4] was not registered for synchronization because synchronization is not active
2025-06-29 00:47:09.543 [39mDEBUG[0;39m [file-async-4] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@737163474 wrapping com.mysql.cj.jdbc.ConnectionImpl@78b35d7b] will not be managed by Spring
2025-06-29 00:47:09.543 [39mDEBUG[0;39m [file-async-4] c.e.p.m.p.P.updateEpisodeObjectKey : ==>  Preparing: UPDATE video_episodes SET object_key = ?, updated_time = NOW() WHERE video_info_id = ? AND number = ?
2025-06-29 00:47:09.544 [39mDEBUG[0;39m [file-async-4] c.e.p.m.p.P.updateEpisodeObjectKey : ==> Parameters: video/fc2d0d36-6137-46dd-9d59-e63339e9896f/1.mp4(String), 81(Long), 1(String)
2025-06-29 00:47:09.567 [39mDEBUG[0;39m [file-async-4] c.e.p.m.p.P.updateEpisodeObjectKey : <==    Updates: 1
2025-06-29 00:47:09.567 [39mDEBUG[0;39m [file-async-4] org.mybatis.spring.SqlSessionUtils : Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1e26f7a4]
2025-06-29 00:47:09.567 [34mINFO [0;39m [file-async-4] c.e.p.s.impl.PureVideoUrlServiceImpl : 异步更新数据库objectKey成功 - videoId: 81, episode: 1, objectKey: video/fc2d0d36-6137-46dd-9d59-e63339e9896f/1.mp4
2025-06-29 00:48:21.928 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Securing POST /api/videoUrl/video-episodes
2025-06-29 00:48:21.928 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-06-29 00:48:21.928 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#uploadVideo(VideoInfoWithEpisodesDto)
2025-06-29 00:48:21.928 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.a.AnonymousAuthenticationFilter : Set SecurityContextHolder to anonymous SecurityContext
2025-06-29 00:48:21.928 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /api/videoUrl/video-episodes] with attributes [permitAll]
2025-06-29 00:48:21.928 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.security.web.FilterChainProxy : Secured POST /api/videoUrl/video-episodes
2025-06-29 00:48:21.929 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : POST "/api/videoUrl/video-episodes", parameters={}
2025-06-29 00:48:21.929 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.PureVideoUrlController#uploadVideo(VideoInfoWithEpisodesDto)
2025-06-29 00:48:21.930 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [VideoInfoWithEpisodesDto(videoEpisodes=[VideoEpisodesDTO(id=null, videoInfoId=null, number=1, object (truncated)...]
2025-06-29 00:48:21.955 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-06-29 00:48:21.956 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7bcb22fd]
2025-06-29 00:48:21.956 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@2123715183 wrapping com.mysql.cj.jdbc.ConnectionImpl@78b35d7b] will be managed by Spring
2025-06-29 00:48:21.956 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.P.findVideoInfoByTitle : ==>  Preparing: SELECT id, title, cover_image_url, description, created_time, updated_time FROM video_info WHERE title = ? LIMIT 1
2025-06-29 00:48:21.956 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.P.findVideoInfoByTitle : ==> Parameters: 记忆缝线(String)
2025-06-29 00:48:21.968 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.P.findVideoInfoByTitle : <==      Total: 1
2025-06-29 00:48:21.968 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7bcb22fd]
2025-06-29 00:48:21.968 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7bcb22fd] from current transaction
2025-06-29 00:48:21.968 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.P.findBasicVideoEpisodesByVideoInfoId : ==>  Preparing: SELECT id, video_info_id, number, object_key, duration, sprite_sheet_object_key, created_time, updated_time FROM video_episodes WHERE video_info_id = ? ORDER BY CAST(number AS SIGNED) ASC
2025-06-29 00:48:21.968 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.P.findBasicVideoEpisodesByVideoInfoId : ==> Parameters: 46(Long)
2025-06-29 00:48:21.980 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.P.findBasicVideoEpisodesByVideoInfoId : <==      Total: 8
2025-06-29 00:48:21.981 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7bcb22fd]
2025-06-29 00:48:21.981 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7bcb22fd] from current transaction
2025-06-29 00:48:21.999 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.P.deleteVideoEpisodesByIds : ==>  Preparing: DELETE FROM video_episodes WHERE id IN ( ? , ? , ? , ? , ? , ? , ? , ? )
2025-06-29 00:48:21.999 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.P.deleteVideoEpisodesByIds : ==> Parameters: 307(Long), 308(Long), 309(Long), 310(Long), 311(Long), 312(Long), 313(Long), 314(Long)
2025-06-29 00:48:22.021 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.P.deleteVideoEpisodesByIds : <==    Updates: 8
2025-06-29 00:48:22.021 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7bcb22fd]
2025-06-29 00:48:22.021 [34mINFO [0;39m [http-nio-8080-exec-1] c.e.p.s.impl.PureVideoUrlServiceImpl : 删除了8个旧分集，视频ID: 46
2025-06-29 00:48:22.021 [34mINFO [0;39m [file-async-6] c.e.p.s.impl.PureVideoUrlServiceImpl : 开始异步下载并上传视频文件 - videoId: 46, episode: 1, url: http://p16-va-tiktok.ibyteimg.com/obj/tos-alisg-v-0051c001-sg/o0QB7yDAf3dD1s5LuEPNzyfMDoBFvcEngIqILK
2025-06-29 00:48:22.021 [34mINFO [0;39m [file-async-3] c.e.p.s.impl.PureVideoUrlServiceImpl : 开始异步生成雪碧图 - videoId: 46, episode: 1
2025-06-29 00:48:22.021 [34mINFO [0;39m [file-async-1] c.e.p.s.impl.PureVideoUrlServiceImpl : 开始异步下载并上传视频文件 - videoId: 46, episode: 2, url: http://p16-va-tiktok.ibyteimg.com/obj/tos-alisg-v-0051c001-sg/oMP5dnGHfABHWMBdDTI4q8EgjiNvDfHbGQiEBF
2025-06-29 00:48:22.021 [34mINFO [0;39m [file-async-2] c.e.p.s.impl.PureVideoUrlServiceImpl : 开始异步生成雪碧图 - videoId: 46, episode: 2
2025-06-29 00:48:22.021 [34mINFO [0;39m [file-async-5] c.e.p.s.impl.PureVideoUrlServiceImpl : 开始异步下载并上传视频文件 - videoId: 46, episode: 3, url: http://p16-va-tiktok.ibyteimg.com/obj/tos-alisg-v-0051c001-sg/owYenzAvGIwqMBa1AoLrgnbBcCeOQfDIIVAdGA
2025-06-29 00:48:22.021 [34mINFO [0;39m [file-async-4] c.e.p.s.impl.PureVideoUrlServiceImpl : 开始异步生成雪碧图 - videoId: 46, episode: 3
2025-06-29 00:48:22.022 [39mDEBUG[0;39m [file-async-5] o.s.w.r.f.client.ExchangeFunctions : [25a86f86] HTTP GET http://p16-va-tiktok.ibyteimg.com/obj/tos-alisg-v-0051c001-sg/owYenzAvGIwqMBa1AoLrgnbBcCeOQfDIIVAdGA
2025-06-29 00:48:22.022 [39mDEBUG[0;39m [file-async-1] o.s.w.r.f.client.ExchangeFunctions : [14655f9f] HTTP GET http://p16-va-tiktok.ibyteimg.com/obj/tos-alisg-v-0051c001-sg/oMP5dnGHfABHWMBdDTI4q8EgjiNvDfHbGQiEBF
2025-06-29 00:48:22.022 [39mDEBUG[0;39m [file-async-6] o.s.w.r.f.client.ExchangeFunctions : [41ddcd5d] HTTP GET http://p16-va-tiktok.ibyteimg.com/obj/tos-alisg-v-0051c001-sg/o0QB7yDAf3dD1s5LuEPNzyfMDoBFvcEngIqILK
2025-06-29 00:48:22.022 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7bcb22fd] from current transaction
2025-06-29 00:48:22.022 [34mINFO [0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl : Executing command: ffmpeg -i http://p16-va-tiktok.ibyteimg.com/obj/tos-alisg-v-0051c001-sg/oMP5dnGHfABHWMBdDTI4q8EgjiNvDfHbGQiEBF -vf fps=1/3,scale=160:-1,tile=23x23 -q:v 2 -y d:\upload\video_sprites\46\output_2.jpg
2025-06-29 00:48:22.022 [34mINFO [0;39m [file-async-3] c.e.p.service.impl.FFmpegServiceImpl : Executing command: ffmpeg -i http://p16-va-tiktok.ibyteimg.com/obj/tos-alisg-v-0051c001-sg/o0QB7yDAf3dD1s5LuEPNzyfMDoBFvcEngIqILK -vf fps=1/3,scale=160:-1,tile=23x23 -q:v 2 -y d:\upload\video_sprites\46\output_1.jpg
2025-06-29 00:48:22.022 [34mINFO [0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl : Executing command: ffmpeg -i http://p16-va-tiktok.ibyteimg.com/obj/tos-alisg-v-0051c001-sg/owYenzAvGIwqMBa1AoLrgnbBcCeOQfDIIVAdGA -vf fps=1/3,scale=160:-1,tile=23x23 -q:v 2 -y d:\upload\video_sprites\46\output_3.jpg
2025-06-29 00:48:22.024 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.P.insertVideoEpisodes : ==>  Preparing: INSERT INTO video_episodes( video_info_id, number, object_key, duration, sprite_sheet_object_key, created_time, updated_time ) VALUES ( ?, ?, ?, ?, ?, NOW(), NOW() ) , ( ?, ?, ?, ?, ?, NOW(), NOW() ) , ( ?, ?, ?, ?, ?, NOW(), NOW() ) , ( ?, ?, ?, ?, ?, NOW(), NOW() ) , ( ?, ?, ?, ?, ?, NOW(), NOW() ) , ( ?, ?, ?, ?, ?, NOW(), NOW() ) , ( ?, ?, ?, ?, ?, NOW(), NOW() ) , ( ?, ?, ?, ?, ?, NOW(), NOW() ) , ( ?, ?, ?, ?, ?, NOW(), NOW() )
2025-06-29 00:48:22.025 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.P.insertVideoEpisodes : ==> Parameters: 46(Long), 1(String), null, null, null, 46(Long), 2(String), null, null, null, 46(Long), 3(String), null, null, null, 46(Long), 4(String), null, null, null, 46(Long), 5(String), null, null, null, 46(Long), 6(String), null, null, null, 46(Long), 7(String), null, null, null, 46(Long), 8(String), null, null, null, 46(Long), 9(String), null, null, null
2025-06-29 00:48:22.046 [39mDEBUG[0;39m [http-nio-8080-exec-1] c.e.p.m.p.P.insertVideoEpisodes : <==    Updates: 9
2025-06-29 00:48:22.047 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7bcb22fd]
2025-06-29 00:48:22.047 [34mINFO [0;39m [http-nio-8080-exec-1] c.e.p.s.impl.PureVideoUrlServiceImpl : 插入视频分集信息成功，共9集，视频ID: 46
2025-06-29 00:48:22.047 [34mINFO [0;39m [http-nio-8080-exec-1] c.e.p.s.impl.PureVideoUrlServiceImpl : 处理了9个新分集，视频ID: 46
2025-06-29 00:48:22.047 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7bcb22fd]
2025-06-29 00:48:22.047 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7bcb22fd]
2025-06-29 00:48:22.047 [39mDEBUG[0;39m [http-nio-8080-exec-1] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7bcb22fd]
2025-06-29 00:48:22.059 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl : ffmpeg version 7.1.1-full_build-www.gyan.dev Copyright (c) 2000-2025 the FFmpeg developers
2025-06-29 00:48:22.059 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :   built with gcc 14.2.0 (Rev1, Built by MSYS2 project)
2025-06-29 00:48:22.059 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :   configuration: --enable-gpl --enable-version3 --enable-static --disable-w32threads --disable-autodetect --enable-fontconfig --enable-iconv --enable-gnutls --enable-lcms2 --enable-libxml2 --enable-gmp --enable-bzlib --enable-lzma --enable-libsnappy --enable-zlib --enable-librist --enable-libsrt --enable-libssh --enable-libzmq --enable-avisynth --enable-libbluray --enable-libcaca --enable-libdvdnav --enable-libdvdread --enable-sdl2 --enable-libaribb24 --enable-libaribcaption --enable-libdav1d --enable-libdavs2 --enable-libopenjpeg --enable-libquirc --enable-libuavs3d --enable-libxevd --enable-libzvbi --enable-libqrencode --enable-librav1e --enable-libsvtav1 --enable-libvvenc --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxavs2 --enable-libxeve --enable-libxvid --enable-libaom --enable-libjxl --enable-libvpx --enable-mediafoundation --enable-libass --enable-frei0r --enable-libfreetype --enable-libfribidi --enable-libharfbuzz --enable-liblensfun --enable-libvidstab --enable-libvmaf --enable-libzimg --enable-amf --enable-cuda-llvm --enable-cuvid --enable-dxva2 --enable-d3d11va --enable-d3d12va --enable-ffnvcodec --enable-libvpl --enable-nvdec --enable-nvenc --enable-vaapi --enable-libshaderc --enable-vulkan --enable-libplacebo --enable-opencl --enable-libcdio --enable-libgme --enable-libmodplug --enable-libopenmpt --enable-libopencore-amrwb --enable-libmp3lame --enable-libshine --enable-libtheora --enable-libtwolame --enable-libvo-amrwbenc --enable-libcodec2 --enable-libilbc --enable-libgsm --enable-liblc3 --enable-libopencore-amrnb --enable-libopus --enable-libspeex --enable-libvorbis --enable-ladspa --enable-libbs2b --enable-libflite --enable-libmysofa --enable-librubberband --enable-libsoxr --enable-chromaprint
2025-06-29 00:48:22.059 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :   libavutil      59. 39.100 / 59. 39.100
2025-06-29 00:48:22.059 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :   libavcodec     61. 19.101 / 61. 19.101
2025-06-29 00:48:22.059 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :   libavformat    61.  7.100 / 61.  7.100
2025-06-29 00:48:22.059 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :   libavdevice    61.  3.100 / 61.  3.100
2025-06-29 00:48:22.059 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :   libavfilter    10.  4.100 / 10.  4.100
2025-06-29 00:48:22.059 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :   libswscale      8.  3.100 /  8.  3.100
2025-06-29 00:48:22.059 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :   libswresample   5.  3.100 /  5.  3.100
2025-06-29 00:48:22.059 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :   libpostproc    58.  3.100 / 58.  3.100
2025-06-29 00:48:22.062 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl : ffmpeg version 7.1.1-full_build-www.gyan.dev Copyright (c) 2000-2025 the FFmpeg developers
2025-06-29 00:48:22.062 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :   built with gcc 14.2.0 (Rev1, Built by MSYS2 project)
2025-06-29 00:48:22.062 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :   configuration: --enable-gpl --enable-version3 --enable-static --disable-w32threads --disable-autodetect --enable-fontconfig --enable-iconv --enable-gnutls --enable-lcms2 --enable-libxml2 --enable-gmp --enable-bzlib --enable-lzma --enable-libsnappy --enable-zlib --enable-librist --enable-libsrt --enable-libssh --enable-libzmq --enable-avisynth --enable-libbluray --enable-libcaca --enable-libdvdnav --enable-libdvdread --enable-sdl2 --enable-libaribb24 --enable-libaribcaption --enable-libdav1d --enable-libdavs2 --enable-libopenjpeg --enable-libquirc --enable-libuavs3d --enable-libxevd --enable-libzvbi --enable-libqrencode --enable-librav1e --enable-libsvtav1 --enable-libvvenc --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxavs2 --enable-libxeve --enable-libxvid --enable-libaom --enable-libjxl --enable-libvpx --enable-mediafoundation --enable-libass --enable-frei0r --enable-libfreetype --enable-libfribidi --enable-libharfbuzz --enable-liblensfun --enable-libvidstab --enable-libvmaf --enable-libzimg --enable-amf --enable-cuda-llvm --enable-cuvid --enable-dxva2 --enable-d3d11va --enable-d3d12va --enable-ffnvcodec --enable-libvpl --enable-nvdec --enable-nvenc --enable-vaapi --enable-libshaderc --enable-vulkan --enable-libplacebo --enable-opencl --enable-libcdio --enable-libgme --enable-libmodplug --enable-libopenmpt --enable-libopencore-amrwb --enable-libmp3lame --enable-libshine --enable-libtheora --enable-libtwolame --enable-libvo-amrwbenc --enable-libcodec2 --enable-libilbc --enable-libgsm --enable-liblc3 --enable-libopencore-amrnb --enable-libopus --enable-libspeex --enable-libvorbis --enable-ladspa --enable-libbs2b --enable-libflite --enable-libmysofa --enable-librubberband --enable-libsoxr --enable-chromaprint
2025-06-29 00:48:22.063 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :   libavutil      59. 39.100 / 59. 39.100
2025-06-29 00:48:22.063 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :   libavcodec     61. 19.101 / 61. 19.101
2025-06-29 00:48:22.063 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :   libavformat    61.  7.100 / 61.  7.100
2025-06-29 00:48:22.063 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :   libavdevice    61.  3.100 / 61.  3.100
2025-06-29 00:48:22.063 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :   libavfilter    10.  4.100 / 10.  4.100
2025-06-29 00:48:22.063 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :   libswscale      8.  3.100 /  8.  3.100
2025-06-29 00:48:22.063 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :   libswresample   5.  3.100 /  5.  3.100
2025-06-29 00:48:22.063 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :   libpostproc    58.  3.100 / 58.  3.100
2025-06-29 00:48:22.064 [39mDEBUG[0;39m [file-async-3] c.e.p.service.impl.FFmpegServiceImpl : ffmpeg version 7.1.1-full_build-www.gyan.dev Copyright (c) 2000-2025 the FFmpeg developers
2025-06-29 00:48:22.064 [39mDEBUG[0;39m [file-async-3] c.e.p.service.impl.FFmpegServiceImpl :   built with gcc 14.2.0 (Rev1, Built by MSYS2 project)
2025-06-29 00:48:22.064 [39mDEBUG[0;39m [file-async-3] c.e.p.service.impl.FFmpegServiceImpl :   configuration: --enable-gpl --enable-version3 --enable-static --disable-w32threads --disable-autodetect --enable-fontconfig --enable-iconv --enable-gnutls --enable-lcms2 --enable-libxml2 --enable-gmp --enable-bzlib --enable-lzma --enable-libsnappy --enable-zlib --enable-librist --enable-libsrt --enable-libssh --enable-libzmq --enable-avisynth --enable-libbluray --enable-libcaca --enable-libdvdnav --enable-libdvdread --enable-sdl2 --enable-libaribb24 --enable-libaribcaption --enable-libdav1d --enable-libdavs2 --enable-libopenjpeg --enable-libquirc --enable-libuavs3d --enable-libxevd --enable-libzvbi --enable-libqrencode --enable-librav1e --enable-libsvtav1 --enable-libvvenc --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxavs2 --enable-libxeve --enable-libxvid --enable-libaom --enable-libjxl --enable-libvpx --enable-mediafoundation --enable-libass --enable-frei0r --enable-libfreetype --enable-libfribidi --enable-libharfbuzz --enable-liblensfun --enable-libvidstab --enable-libvmaf --enable-libzimg --enable-amf --enable-cuda-llvm --enable-cuvid --enable-dxva2 --enable-d3d11va --enable-d3d12va --enable-ffnvcodec --enable-libvpl --enable-nvdec --enable-nvenc --enable-vaapi --enable-libshaderc --enable-vulkan --enable-libplacebo --enable-opencl --enable-libcdio --enable-libgme --enable-libmodplug --enable-libopenmpt --enable-libopencore-amrwb --enable-libmp3lame --enable-libshine --enable-libtheora --enable-libtwolame --enable-libvo-amrwbenc --enable-libcodec2 --enable-libilbc --enable-libgsm --enable-liblc3 --enable-libopencore-amrnb --enable-libopus --enable-libspeex --enable-libvorbis --enable-ladspa --enable-libbs2b --enable-libflite --enable-libmysofa --enable-librubberband --enable-libsoxr --enable-chromaprint
2025-06-29 00:48:22.064 [39mDEBUG[0;39m [file-async-3] c.e.p.service.impl.FFmpegServiceImpl :   libavutil      59. 39.100 / 59. 39.100
2025-06-29 00:48:22.064 [39mDEBUG[0;39m [file-async-3] c.e.p.service.impl.FFmpegServiceImpl :   libavcodec     61. 19.101 / 61. 19.101
2025-06-29 00:48:22.064 [39mDEBUG[0;39m [file-async-3] c.e.p.service.impl.FFmpegServiceImpl :   libavformat    61.  7.100 / 61.  7.100
2025-06-29 00:48:22.064 [39mDEBUG[0;39m [file-async-3] c.e.p.service.impl.FFmpegServiceImpl :   libavdevice    61.  3.100 / 61.  3.100
2025-06-29 00:48:22.064 [39mDEBUG[0;39m [file-async-3] c.e.p.service.impl.FFmpegServiceImpl :   libavfilter    10.  4.100 / 10.  4.100
2025-06-29 00:48:22.064 [39mDEBUG[0;39m [file-async-3] c.e.p.service.impl.FFmpegServiceImpl :   libswscale      8.  3.100 /  8.  3.100
2025-06-29 00:48:22.064 [39mDEBUG[0;39m [file-async-3] c.e.p.service.impl.FFmpegServiceImpl :   libswresample   5.  3.100 /  5.  3.100
2025-06-29 00:48:22.064 [39mDEBUG[0;39m [file-async-3] c.e.p.service.impl.FFmpegServiceImpl :   libpostproc    58.  3.100 / 58.  3.100
2025-06-29 00:48:22.085 [39mDEBUG[0;39m [reactor-http-nio-3] o.s.w.r.f.client.ExchangeFunctions : [14655f9f] [00a8eab8-2] Response 200 OK
2025-06-29 00:48:22.085 [39mDEBUG[0;39m [reactor-http-nio-4] o.s.w.r.f.client.ExchangeFunctions : [25a86f86] [c375baaf-2] Response 200 OK
2025-06-29 00:48:22.088 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-06-29 00:48:22.088 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=视频上传成功, success=true, data=null, time=2025-06-28T16:48:22.087481Z)]
2025-06-29 00:48:22.089 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-06-29 00:48:22.089 [39mDEBUG[0;39m [http-nio-8080-exec-1] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-06-29 00:48:22.876 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl : Input #0, mov,mp4,m4a,3gp,3g2,mj2, from 'http://p16-va-tiktok.ibyteimg.com/obj/tos-alisg-v-0051c001-sg/owYenzAvGIwqMBa1AoLrgnbBcCeOQfDIIVAdGA':
2025-06-29 00:48:22.876 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :   Metadata:
2025-06-29 00:48:22.876 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :     major_brand     : isom
2025-06-29 00:48:22.876 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :     minor_version   : 1
2025-06-29 00:48:22.876 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :     compatible_brands: isomavc1
2025-06-29 00:48:22.876 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :     creation_time   : 2025-04-17T20:09:18.000000Z
2025-06-29 00:48:22.876 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :   Duration: 00:23:45.09, start: 0.000000, bitrate: 2256 kb/s
2025-06-29 00:48:22.876 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :   Stream #0:0[0x1](und): Video: h264 (High) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 1920x1080, 2057 kb/s, 23.98 fps, 23.98 tbr, 96k tbn (default)
2025-06-29 00:48:22.876 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :       Metadata:
2025-06-29 00:48:22.876 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :         creation_time   : 2025-04-17T20:09:18.000000Z
2025-06-29 00:48:22.876 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :         handler_name    : 264@GPAC0.8.0-rev95-g00dfc933-master
2025-06-29 00:48:22.876 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :         vendor_id       : [0][0][0][0]
2025-06-29 00:48:22.876 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :   Stream #0:1[0x2](und): Audio: aac (LC) (mp4a / 0x6134706D), 44100 Hz, stereo, fltp, 195 kb/s (default)
2025-06-29 00:48:22.876 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :       Metadata:
2025-06-29 00:48:22.877 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :         creation_time   : 2025-04-17T20:09:21.000000Z
2025-06-29 00:48:22.877 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :         handler_name    : aac@GPAC0.8.0-rev95-g00dfc933-master
2025-06-29 00:48:22.877 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :         vendor_id       : [0][0][0][0]
2025-06-29 00:48:22.882 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl : Stream mapping:
2025-06-29 00:48:22.882 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl :   Stream #0:0 -> #0:0 (h264 (native) -> mjpeg (native))
2025-06-29 00:48:22.882 [39mDEBUG[0;39m [file-async-4] c.e.p.service.impl.FFmpegServiceImpl : Press [q] to stop, [?] for help
2025-06-29 00:48:22.981 [39mDEBUG[0;39m [file-async-3] c.e.p.service.impl.FFmpegServiceImpl : Input #0, mov,mp4,m4a,3gp,3g2,mj2, from 'http://p16-va-tiktok.ibyteimg.com/obj/tos-alisg-v-0051c001-sg/o0QB7yDAf3dD1s5LuEPNzyfMDoBFvcEngIqILK':
2025-06-29 00:48:22.981 [39mDEBUG[0;39m [file-async-3] c.e.p.service.impl.FFmpegServiceImpl :   Metadata:
2025-06-29 00:48:22.981 [39mDEBUG[0;39m [file-async-3] c.e.p.service.impl.FFmpegServiceImpl :     major_brand     : isom
2025-06-29 00:48:22.981 [39mDEBUG[0;39m [file-async-3] c.e.p.service.impl.FFmpegServiceImpl :     minor_version   : 1
2025-06-29 00:48:22.981 [39mDEBUG[0;39m [file-async-3] c.e.p.service.impl.FFmpegServiceImpl :     compatible_brands: isomavc1
2025-06-29 00:48:22.981 [39mDEBUG[0;39m [file-async-3] c.e.p.service.impl.FFmpegServiceImpl :     creation_time   : 2025-04-04T03:17:31.000000Z
2025-06-29 00:48:22.981 [39mDEBUG[0;39m [file-async-3] c.e.p.service.impl.FFmpegServiceImpl :   Duration: 00:23:45.05, start: 0.000000, bitrate: 2649 kb/s
2025-06-29 00:48:22.981 [39mDEBUG[0;39m [file-async-3] c.e.p.service.impl.FFmpegServiceImpl :   Stream #0:0[0x1](und): Video: h264 (High) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 1920x1080, 2450 kb/s, 23.98 fps, 23.98 tbr, 96k tbn (default)
2025-06-29 00:48:22.981 [39mDEBUG[0;39m [file-async-3] c.e.p.service.impl.FFmpegServiceImpl :       Metadata:
2025-06-29 00:48:22.981 [39mDEBUG[0;39m [file-async-3] c.e.p.service.impl.FFmpegServiceImpl :         creation_time   : 2025-04-04T03:17:31.000000Z
2025-06-29 00:48:22.981 [39mDEBUG[0;39m [file-async-3] c.e.p.service.impl.FFmpegServiceImpl :         handler_name    : 264@GPAC0.8.0-rev95-g00dfc933-master
2025-06-29 00:48:22.981 [39mDEBUG[0;39m [file-async-3] c.e.p.service.impl.FFmpegServiceImpl :         vendor_id       : [0][0][0][0]
2025-06-29 00:48:22.981 [39mDEBUG[0;39m [file-async-3] c.e.p.service.impl.FFmpegServiceImpl :   Stream #0:1[0x2](und): Audio: aac (LC) (mp4a / 0x6134706D), 44100 Hz, stereo, fltp, 195 kb/s (default)
2025-06-29 00:48:22.981 [39mDEBUG[0;39m [file-async-3] c.e.p.service.impl.FFmpegServiceImpl :       Metadata:
2025-06-29 00:48:22.981 [39mDEBUG[0;39m [file-async-3] c.e.p.service.impl.FFmpegServiceImpl :         creation_time   : 2025-04-04T03:17:34.000000Z
2025-06-29 00:48:22.981 [39mDEBUG[0;39m [file-async-3] c.e.p.service.impl.FFmpegServiceImpl :         handler_name    : aac@GPAC0.8.0-rev95-g00dfc933-master
2025-06-29 00:48:22.981 [39mDEBUG[0;39m [file-async-3] c.e.p.service.impl.FFmpegServiceImpl :         vendor_id       : [0][0][0][0]
2025-06-29 00:48:22.986 [39mDEBUG[0;39m [file-async-3] c.e.p.service.impl.FFmpegServiceImpl : Stream mapping:
2025-06-29 00:48:22.986 [39mDEBUG[0;39m [file-async-3] c.e.p.service.impl.FFmpegServiceImpl :   Stream #0:0 -> #0:0 (h264 (native) -> mjpeg (native))
2025-06-29 00:48:22.986 [39mDEBUG[0;39m [file-async-3] c.e.p.service.impl.FFmpegServiceImpl : Press [q] to stop, [?] for help
2025-06-29 00:48:23.057 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl : Input #0, mov,mp4,m4a,3gp,3g2,mj2, from 'http://p16-va-tiktok.ibyteimg.com/obj/tos-alisg-v-0051c001-sg/oMP5dnGHfABHWMBdDTI4q8EgjiNvDfHbGQiEBF':
2025-06-29 00:48:23.058 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :   Metadata:
2025-06-29 00:48:23.058 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :     major_brand     : isom
2025-06-29 00:48:23.058 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :     minor_version   : 1
2025-06-29 00:48:23.058 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :     compatible_brands: isomavc1
2025-06-29 00:48:23.058 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :     creation_time   : 2025-04-10T23:15:12.000000Z
2025-06-29 00:48:23.058 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :   Duration: 00:23:45.09, start: 0.000000, bitrate: 2419 kb/s
2025-06-29 00:48:23.058 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :   Stream #0:0[0x1](und): Video: h264 (High) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 1920x1080, 2220 kb/s, 23.98 fps, 23.98 tbr, 96k tbn (default)
2025-06-29 00:48:23.058 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :       Metadata:
2025-06-29 00:48:23.058 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :         creation_time   : 2025-04-10T23:15:12.000000Z
2025-06-29 00:48:23.058 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :         handler_name    : 264@GPAC0.8.0-rev95-g00dfc933-master
2025-06-29 00:48:23.058 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :         vendor_id       : [0][0][0][0]
2025-06-29 00:48:23.058 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :   Stream #0:1[0x2](und): Audio: aac (LC) (mp4a / 0x6134706D), 44100 Hz, stereo, fltp, 195 kb/s (default)
2025-06-29 00:48:23.058 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :       Metadata:
2025-06-29 00:48:23.058 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :         creation_time   : 2025-04-10T23:15:15.000000Z
2025-06-29 00:48:23.058 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :         handler_name    : aac@GPAC0.8.0-rev95-g00dfc933-master
2025-06-29 00:48:23.058 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :         vendor_id       : [0][0][0][0]
2025-06-29 00:48:23.062 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl : Stream mapping:
2025-06-29 00:48:23.062 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl :   Stream #0:0 -> #0:0 (h264 (native) -> mjpeg (native))
2025-06-29 00:48:23.062 [39mDEBUG[0;39m [file-async-2] c.e.p.service.impl.FFmpegServiceImpl : Press [q] to stop, [?] for help
2025-06-29 00:48:23.264 [1;31mERROR[0;39m [file-async-1] c.e.p.s.impl.PureVideoUrlServiceImpl : 异步下载并上传视频文件失败 - videoId: 46, episode: 2, 错误: 200 OK from GET http://p16-va-tiktok.ibyteimg.com/obj/tos-alisg-v-0051c001-sg/oMP5dnGHfABHWMBdDTI4q8EgjiNvDfHbGQiEBF; nested exception is org.springframework.core.io.buffer.DataBufferLimitException: Exceeded limit on max bytes to buffer : 16777216
org.springframework.web.reactive.function.client.WebClientResponseException: 200 OK from GET http://p16-va-tiktok.ibyteimg.com/obj/tos-alisg-v-0051c001-sg/oMP5dnGHfABHWMBdDTI4q8EgjiNvDfHbGQiEBF; nested exception is org.springframework.core.io.buffer.DataBufferLimitException: Exceeded limit on max bytes to buffer : 16777216
	at org.springframework.web.reactive.function.client.WebClientResponseException.create(WebClientResponseException.java:250)
	at org.springframework.web.reactive.function.client.DefaultClientResponse.lambda$createException$1(DefaultClientResponse.java:207)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:106)
	at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onError(FluxOnErrorReturn.java:199)
	at reactor.core.publisher.Operators$MonoSubscriber.onError(Operators.java:1886)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onError(FluxOnAssembly.java:544)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onError(FluxMapFuseable.java:142)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onError(FluxContextWrite.java:121)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onError(FluxMapFuseable.java:340)
	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onError(FluxFilterFuseable.java:382)
	at reactor.core.publisher.MonoCollect$CollectSubscriber.onError(MonoCollect.java:145)
	at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
	at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
	at reactor.core.publisher.Operators.error(Operators.java:198)
	at reactor.netty.channel.FluxReceive.startReceiver(FluxReceive.java:191)
	at reactor.netty.channel.FluxReceive.subscribe(FluxReceive.java:147)
	at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:62)
	at reactor.netty.ByteBufFlux.subscribe(ByteBufFlux.java:340)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4490)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onError(FluxOnAssembly.java:544)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onError(FluxMapFuseable.java:142)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onError(FluxContextWrite.java:121)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onError(FluxMapFuseable.java:340)
	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onError(FluxFilterFuseable.java:382)
	at reactor.core.publisher.MonoCollect$CollectSubscriber.onError(MonoCollect.java:145)
	at reactor.core.publisher.MonoCollect$CollectSubscriber.onNext(MonoCollect.java:124)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onNext(FluxPeek.java:200)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
	at reactor.netty.channel.FluxReceive.onInboundNext(FluxReceive.java:379)
	at reactor.netty.channel.ChannelOperations.onInboundNext(ChannelOperations.java:419)
	at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:790)
	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:114)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:103)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:829)
	Suppressed: java.lang.Exception: #block terminated with an error
		at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:100)
		at reactor.core.publisher.Mono.block(Mono.java:1742)
		at com.example.pure.service.impl.PureVideoUrlServiceImpl.downloadAndUploadVideoAsync(PureVideoUrlServiceImpl.java:516)
		at com.example.pure.service.impl.PureVideoUrlServiceImpl$$FastClassBySpringCGLIB$$6da7a9f9.invoke(<generated>)
		at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
		at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
		at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
		at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
		at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
		at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
		at org.springframework.security.concurrent.DelegatingSecurityContextRunnable.run(DelegatingSecurityContextRunnable.java:82)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
		... 1 common frames omitted
Caused by: org.springframework.core.io.buffer.DataBufferLimitException: Exceeded limit on max bytes to buffer : 16777216
	at org.springframework.core.io.buffer.LimitedDataBufferList.raiseLimitException(LimitedDataBufferList.java:99)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Body from GET http://p16-va-tiktok.ibyteimg.com/obj/tos-alisg-v-0051c001-sg/oMP5dnGHfABHWMBdDTI4q8EgjiNvDfHbGQiEBF [DefaultClientResponse]
Original Stack Trace:
		at org.springframework.core.io.buffer.LimitedDataBufferList.raiseLimitException(LimitedDataBufferList.java:99)
		at org.springframework.core.io.buffer.LimitedDataBufferList.updateCount(LimitedDataBufferList.java:92)
		at org.springframework.core.io.buffer.LimitedDataBufferList.add(LimitedDataBufferList.java:58)
		at reactor.core.publisher.MonoCollect$CollectSubscriber.onNext(MonoCollect.java:119)
		at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onNext(FluxPeek.java:200)
		at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
		at reactor.netty.channel.FluxReceive.onInboundNext(FluxReceive.java:379)
		at reactor.netty.channel.ChannelOperations.onInboundNext(ChannelOperations.java:419)
		at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:790)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:114)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:103)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
		at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:829)
2025-06-29 00:48:23.264 [34mINFO [0;39m [file-async-1] c.e.p.s.impl.PureVideoUrlServiceImpl : 开始异步下载并上传视频文件 - videoId: 46, episode: 4, url: http://p16-va-tiktok.ibyteimg.com/obj/tos-alisg-v-0051c001-sg/oIw8Liiq0A7lQokxABAk419BiVAEoEfmEBayMy
2025-06-29 00:48:23.265 [39mDEBUG[0;39m [file-async-1] o.s.w.r.f.client.ExchangeFunctions : [3d2d2a8e] HTTP GET http://p16-va-tiktok.ibyteimg.com/obj/tos-alisg-v-0051c001-sg/oIw8Liiq0A7lQokxABAk419BiVAEoEfmEBayMy
2025-06-29 00:48:23.801 [1;31mERROR[0;39m [file-async-5] c.e.p.s.impl.PureVideoUrlServiceImpl : 异步下载并上传视频文件失败 - videoId: 46, episode: 3, 错误: 200 OK from GET http://p16-va-tiktok.ibyteimg.com/obj/tos-alisg-v-0051c001-sg/owYenzAvGIwqMBa1AoLrgnbBcCeOQfDIIVAdGA; nested exception is org.springframework.core.io.buffer.DataBufferLimitException: Exceeded limit on max bytes to buffer : 16777216
org.springframework.web.reactive.function.client.WebClientResponseException: 200 OK from GET http://p16-va-tiktok.ibyteimg.com/obj/tos-alisg-v-0051c001-sg/owYenzAvGIwqMBa1AoLrgnbBcCeOQfDIIVAdGA; nested exception is org.springframework.core.io.buffer.DataBufferLimitException: Exceeded limit on max bytes to buffer : 16777216
	at org.springframework.web.reactive.function.client.WebClientResponseException.create(WebClientResponseException.java:250)
	at org.springframework.web.reactive.function.client.DefaultClientResponse.lambda$createException$1(DefaultClientResponse.java:207)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:106)
	at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onError(FluxOnErrorReturn.java:199)
	at reactor.core.publisher.Operators$MonoSubscriber.onError(Operators.java:1886)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onError(FluxOnAssembly.java:544)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onError(FluxMapFuseable.java:142)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onError(FluxContextWrite.java:121)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onError(FluxMapFuseable.java:340)
	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onError(FluxFilterFuseable.java:382)
	at reactor.core.publisher.MonoCollect$CollectSubscriber.onError(MonoCollect.java:145)
	at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
	at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
	at reactor.core.publisher.Operators.error(Operators.java:198)
	at reactor.netty.channel.FluxReceive.startReceiver(FluxReceive.java:191)
	at reactor.netty.channel.FluxReceive.subscribe(FluxReceive.java:147)
	at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:62)
	at reactor.netty.ByteBufFlux.subscribe(ByteBufFlux.java:340)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4490)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onError(FluxOnAssembly.java:544)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onError(FluxMapFuseable.java:142)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onError(FluxContextWrite.java:121)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onError(FluxMapFuseable.java:340)
	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onError(FluxFilterFuseable.java:382)
	at reactor.core.publisher.MonoCollect$CollectSubscriber.onError(MonoCollect.java:145)
	at reactor.core.publisher.MonoCollect$CollectSubscriber.onNext(MonoCollect.java:124)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onNext(FluxPeek.java:200)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
	at reactor.netty.channel.FluxReceive.onInboundNext(FluxReceive.java:379)
	at reactor.netty.channel.ChannelOperations.onInboundNext(ChannelOperations.java:419)
	at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:790)
	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:114)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:103)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:333)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:454)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:290)
	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:829)
	Suppressed: java.lang.Exception: #block terminated with an error
		at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:100)
		at reactor.core.publisher.Mono.block(Mono.java:1742)
		at com.example.pure.service.impl.PureVideoUrlServiceImpl.downloadAndUploadVideoAsync(PureVideoUrlServiceImpl.java:516)
		at com.example.pure.service.impl.PureVideoUrlServiceImpl$$FastClassBySpringCGLIB$$6da7a9f9.invoke(<generated>)
		at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
		at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
		at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
		at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
		at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
		at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
		at org.springframework.security.concurrent.DelegatingSecurityContextRunnable.run(DelegatingSecurityContextRunnable.java:82)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
		... 1 common frames omitted
Caused by: org.springframework.core.io.buffer.DataBufferLimitException: Exceeded limit on max bytes to buffer : 16777216
	at org.springframework.core.io.buffer.LimitedDataBufferList.raiseLimitException(LimitedDataBufferList.java:99)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Body from GET http://p16-va-tiktok.ibyteimg.com/obj/tos-alisg-v-0051c001-sg/owYenzAvGIwqMBa1AoLrgnbBcCeOQfDIIVAdGA [DefaultClientResponse]
Original Stack Trace:
		at org.springframework.core.io.buffer.LimitedDataBufferList.raiseLimitException(LimitedDataBufferList.java:99)
		at org.springframework.core.io.buffer.LimitedDataBufferList.updateCount(LimitedDataBufferList.java:92)
		at org.springframework.core.io.buffer.LimitedDataBufferList.add(LimitedDataBufferList.java:58)
		at reactor.core.publisher.MonoCollect$CollectSubscriber.onNext(MonoCollect.java:119)
		at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onNext(FluxPeek.java:200)
		at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
		at reactor.netty.channel.FluxReceive.onInboundNext(FluxReceive.java:379)
		at reactor.netty.channel.ChannelOperations.onInboundNext(ChannelOperations.java:419)
		at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:790)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:114)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:103)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:333)
		at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:454)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:290)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
		at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:829)
2025-06-29 00:48:23.801 [34mINFO [0;39m [file-async-5] c.e.p.s.impl.PureVideoUrlServiceImpl : 开始异步生成雪碧图 - videoId: 46, episode: 4
2025-06-29 00:48:23.802 [34mINFO [0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl : Executing command: ffmpeg -i http://p16-va-tiktok.ibyteimg.com/obj/tos-alisg-v-0051c001-sg/oIw8Liiq0A7lQokxABAk419BiVAEoEfmEBayMy -vf fps=1/3,scale=160:-1,tile=23x23 -q:v 2 -y d:\upload\video_sprites\46\output_4.jpg
2025-06-29 00:48:23.856 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl : ffmpeg version 7.1.1-full_build-www.gyan.dev Copyright (c) 2000-2025 the FFmpeg developers
2025-06-29 00:48:23.856 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :   built with gcc 14.2.0 (Rev1, Built by MSYS2 project)
2025-06-29 00:48:23.856 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :   configuration: --enable-gpl --enable-version3 --enable-static --disable-w32threads --disable-autodetect --enable-fontconfig --enable-iconv --enable-gnutls --enable-lcms2 --enable-libxml2 --enable-gmp --enable-bzlib --enable-lzma --enable-libsnappy --enable-zlib --enable-librist --enable-libsrt --enable-libssh --enable-libzmq --enable-avisynth --enable-libbluray --enable-libcaca --enable-libdvdnav --enable-libdvdread --enable-sdl2 --enable-libaribb24 --enable-libaribcaption --enable-libdav1d --enable-libdavs2 --enable-libopenjpeg --enable-libquirc --enable-libuavs3d --enable-libxevd --enable-libzvbi --enable-libqrencode --enable-librav1e --enable-libsvtav1 --enable-libvvenc --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxavs2 --enable-libxeve --enable-libxvid --enable-libaom --enable-libjxl --enable-libvpx --enable-mediafoundation --enable-libass --enable-frei0r --enable-libfreetype --enable-libfribidi --enable-libharfbuzz --enable-liblensfun --enable-libvidstab --enable-libvmaf --enable-libzimg --enable-amf --enable-cuda-llvm --enable-cuvid --enable-dxva2 --enable-d3d11va --enable-d3d12va --enable-ffnvcodec --enable-libvpl --enable-nvdec --enable-nvenc --enable-vaapi --enable-libshaderc --enable-vulkan --enable-libplacebo --enable-opencl --enable-libcdio --enable-libgme --enable-libmodplug --enable-libopenmpt --enable-libopencore-amrwb --enable-libmp3lame --enable-libshine --enable-libtheora --enable-libtwolame --enable-libvo-amrwbenc --enable-libcodec2 --enable-libilbc --enable-libgsm --enable-liblc3 --enable-libopencore-amrnb --enable-libopus --enable-libspeex --enable-libvorbis --enable-ladspa --enable-libbs2b --enable-libflite --enable-libmysofa --enable-librubberband --enable-libsoxr --enable-chromaprint
2025-06-29 00:48:23.856 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :   libavutil      59. 39.100 / 59. 39.100
2025-06-29 00:48:23.856 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :   libavcodec     61. 19.101 / 61. 19.101
2025-06-29 00:48:23.856 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :   libavformat    61.  7.100 / 61.  7.100
2025-06-29 00:48:23.856 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :   libavdevice    61.  3.100 / 61.  3.100
2025-06-29 00:48:23.856 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :   libavfilter    10.  4.100 / 10.  4.100
2025-06-29 00:48:23.856 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :   libswscale      8.  3.100 /  8.  3.100
2025-06-29 00:48:23.856 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :   libswresample   5.  3.100 /  5.  3.100
2025-06-29 00:48:23.856 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :   libpostproc    58.  3.100 / 58.  3.100
2025-06-29 00:48:24.648 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl : Input #0, mov,mp4,m4a,3gp,3g2,mj2, from 'http://p16-va-tiktok.ibyteimg.com/obj/tos-alisg-v-0051c001-sg/oIw8Liiq0A7lQokxABAk419BiVAEoEfmEBayMy':
2025-06-29 00:48:24.648 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :   Metadata:
2025-06-29 00:48:24.648 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :     major_brand     : isom
2025-06-29 00:48:24.648 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :     minor_version   : 1
2025-06-29 00:48:24.648 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :     compatible_brands: isomavc1
2025-06-29 00:48:24.648 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :     creation_time   : 2025-04-26T00:29:54.000000Z
2025-06-29 00:48:24.648 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :   Duration: 00:23:45.05, start: 0.000000, bitrate: 2242 kb/s
2025-06-29 00:48:24.649 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :   Stream #0:0[0x1](und): Video: h264 (High) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 1920x1080, 2042 kb/s, 23.98 fps, 23.98 tbr, 96k tbn (default)
2025-06-29 00:48:24.649 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :       Metadata:
2025-06-29 00:48:24.649 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :         creation_time   : 2025-04-26T00:29:54.000000Z
2025-06-29 00:48:24.649 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :         handler_name    : 264@GPAC0.8.0-rev95-g00dfc933-master
2025-06-29 00:48:24.649 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :         vendor_id       : [0][0][0][0]
2025-06-29 00:48:24.649 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :   Stream #0:1[0x2](und): Audio: aac (LC) (mp4a / 0x6134706D), 44100 Hz, stereo, fltp, 196 kb/s (default)
2025-06-29 00:48:24.649 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :       Metadata:
2025-06-29 00:48:24.649 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :         creation_time   : 2025-04-26T00:29:57.000000Z
2025-06-29 00:48:24.649 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :         handler_name    : aac@GPAC0.8.0-rev95-g00dfc933-master
2025-06-29 00:48:24.649 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :         vendor_id       : [0][0][0][0]
2025-06-29 00:48:24.676 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl : Stream mapping:
2025-06-29 00:48:24.676 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl :   Stream #0:0 -> #0:0 (h264 (native) -> mjpeg (native))
2025-06-29 00:48:24.677 [39mDEBUG[0;39m [file-async-5] c.e.p.service.impl.FFmpegServiceImpl : Press [q] to stop, [?] for help
2025-06-29 00:48:27.192 [39mDEBUG[0;39m [reactor-http-nio-6] o.s.w.r.f.client.ExchangeFunctions : [3d2d2a8e] [3a1edd1a-1] Response 200 OK
2025-06-29 00:48:27.197 [39mDEBUG[0;39m [reactor-http-nio-5] o.s.w.r.f.client.ExchangeFunctions : [41ddcd5d] [109a89b6-1] Response 200 OK
2025-06-29 00:48:29.311 [1;31mERROR[0;39m [file-async-6] c.e.p.s.impl.PureVideoUrlServiceImpl : 异步下载并上传视频文件失败 - videoId: 46, episode: 1, 错误: 200 OK from GET http://p16-va-tiktok.ibyteimg.com/obj/tos-alisg-v-0051c001-sg/o0QB7yDAf3dD1s5LuEPNzyfMDoBFvcEngIqILK; nested exception is org.springframework.core.io.buffer.DataBufferLimitException: Exceeded limit on max bytes to buffer : 16777216
org.springframework.web.reactive.function.client.WebClientResponseException: 200 OK from GET http://p16-va-tiktok.ibyteimg.com/obj/tos-alisg-v-0051c001-sg/o0QB7yDAf3dD1s5LuEPNzyfMDoBFvcEngIqILK; nested exception is org.springframework.core.io.buffer.DataBufferLimitException: Exceeded limit on max bytes to buffer : 16777216
	at org.springframework.web.reactive.function.client.WebClientResponseException.create(WebClientResponseException.java:250)
	at org.springframework.web.reactive.function.client.DefaultClientResponse.lambda$createException$1(DefaultClientResponse.java:207)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:106)
	at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onError(FluxOnErrorReturn.java:199)
	at reactor.core.publisher.Operators$MonoSubscriber.onError(Operators.java:1886)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onError(FluxOnAssembly.java:544)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onError(FluxMapFuseable.java:142)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onError(FluxContextWrite.java:121)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onError(FluxMapFuseable.java:340)
	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onError(FluxFilterFuseable.java:382)
	at reactor.core.publisher.MonoCollect$CollectSubscriber.onError(MonoCollect.java:145)
	at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
	at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
	at reactor.core.publisher.Operators.error(Operators.java:198)
	at reactor.netty.channel.FluxReceive.startReceiver(FluxReceive.java:191)
	at reactor.netty.channel.FluxReceive.subscribe(FluxReceive.java:147)
	at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:62)
	at reactor.netty.ByteBufFlux.subscribe(ByteBufFlux.java:340)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4490)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onError(FluxOnAssembly.java:544)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onError(FluxMapFuseable.java:142)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onError(FluxContextWrite.java:121)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onError(FluxMapFuseable.java:340)
	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onError(FluxFilterFuseable.java:382)
	at reactor.core.publisher.MonoCollect$CollectSubscriber.onError(MonoCollect.java:145)
	at reactor.core.publisher.MonoCollect$CollectSubscriber.onNext(MonoCollect.java:124)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onNext(FluxPeek.java:200)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
	at reactor.netty.channel.FluxReceive.onInboundNext(FluxReceive.java:379)
	at reactor.netty.channel.ChannelOperations.onInboundNext(ChannelOperations.java:419)
	at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:790)
	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:114)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:103)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:333)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:454)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:290)
	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:829)
	Suppressed: java.lang.Exception: #block terminated with an error
		at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:100)
		at reactor.core.publisher.Mono.block(Mono.java:1742)
		at com.example.pure.service.impl.PureVideoUrlServiceImpl.downloadAndUploadVideoAsync(PureVideoUrlServiceImpl.java:516)
		at com.example.pure.service.impl.PureVideoUrlServiceImpl$$FastClassBySpringCGLIB$$6da7a9f9.invoke(<generated>)
		at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
		at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
		at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
		at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
		at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
		at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
		at org.springframework.security.concurrent.DelegatingSecurityContextRunnable.run(DelegatingSecurityContextRunnable.java:82)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
		... 1 common frames omitted
Caused by: org.springframework.core.io.buffer.DataBufferLimitException: Exceeded limit on max bytes to buffer : 16777216
	at org.springframework.core.io.buffer.LimitedDataBufferList.raiseLimitException(LimitedDataBufferList.java:99)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Body from GET http://p16-va-tiktok.ibyteimg.com/obj/tos-alisg-v-0051c001-sg/o0QB7yDAf3dD1s5LuEPNzyfMDoBFvcEngIqILK [DefaultClientResponse]
Original Stack Trace:
		at org.springframework.core.io.buffer.LimitedDataBufferList.raiseLimitException(LimitedDataBufferList.java:99)
		at org.springframework.core.io.buffer.LimitedDataBufferList.updateCount(LimitedDataBufferList.java:92)
		at org.springframework.core.io.buffer.LimitedDataBufferList.add(LimitedDataBufferList.java:58)
		at reactor.core.publisher.MonoCollect$CollectSubscriber.onNext(MonoCollect.java:119)
		at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onNext(FluxPeek.java:200)
		at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
		at reactor.netty.channel.FluxReceive.onInboundNext(FluxReceive.java:379)
		at reactor.netty.channel.ChannelOperations.onInboundNext(ChannelOperations.java:419)
		at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:790)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:114)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:103)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:333)
		at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:454)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:290)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
		at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:829)
2025-06-29 00:48:29.312 [34mINFO [0;39m [file-async-6] c.e.p.s.impl.PureVideoUrlServiceImpl : 开始异步下载并上传视频文件 - videoId: 46, episode: 5, url: http://p16-va-tiktok.ibyteimg.com/obj/tos-alisg-v-0051c001-sg/o0MdEZi19IomUUpSAXBAEBC9m90wQAcBXSikf4
2025-06-29 00:48:29.313 [39mDEBUG[0;39m [file-async-6] o.s.w.r.f.client.ExchangeFunctions : [1daaee79] HTTP GET http://p16-va-tiktok.ibyteimg.com/obj/tos-alisg-v-0051c001-sg/o0MdEZi19IomUUpSAXBAEBC9m90wQAcBXSikf4
2025-06-29 00:48:33.290 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel removed SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-06-29 00:48:33.290 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-06-29 00:48:33.290 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopping...
2025-06-29 00:48:33.290 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@794b139b]]
2025-06-29 00:48:33.290 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@794b139b]
2025-06-29 00:48:33.290 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@794b139b]
2025-06-29 00:48:33.290 [34mINFO [0;39m [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopped.
2025-06-29 00:48:33.290 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-29 00:48:33.290 [39mDEBUG[0;39m [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-29 00:48:33.909 [1;31mERROR[0;39m [file-async-1] c.e.p.s.impl.PureVideoUrlServiceImpl : 异步下载并上传视频文件失败 - videoId: 46, episode: 4, 错误: 200 OK from GET http://p16-va-tiktok.ibyteimg.com/obj/tos-alisg-v-0051c001-sg/oIw8Liiq0A7lQokxABAk419BiVAEoEfmEBayMy; nested exception is org.springframework.core.io.buffer.DataBufferLimitException: Exceeded limit on max bytes to buffer : 16777216
org.springframework.web.reactive.function.client.WebClientResponseException: 200 OK from GET http://p16-va-tiktok.ibyteimg.com/obj/tos-alisg-v-0051c001-sg/oIw8Liiq0A7lQokxABAk419BiVAEoEfmEBayMy; nested exception is org.springframework.core.io.buffer.DataBufferLimitException: Exceeded limit on max bytes to buffer : 16777216
	at org.springframework.web.reactive.function.client.WebClientResponseException.create(WebClientResponseException.java:250)
	at org.springframework.web.reactive.function.client.DefaultClientResponse.lambda$createException$1(DefaultClientResponse.java:207)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:106)
	at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onError(FluxOnErrorReturn.java:199)
	at reactor.core.publisher.Operators$MonoSubscriber.onError(Operators.java:1886)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onError(FluxOnAssembly.java:544)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onError(FluxMapFuseable.java:142)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onError(FluxContextWrite.java:121)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onError(FluxMapFuseable.java:340)
	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onError(FluxFilterFuseable.java:382)
	at reactor.core.publisher.MonoCollect$CollectSubscriber.onError(MonoCollect.java:145)
	at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
	at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
	at reactor.core.publisher.Operators.error(Operators.java:198)
	at reactor.netty.channel.FluxReceive.startReceiver(FluxReceive.java:191)
	at reactor.netty.channel.FluxReceive.subscribe(FluxReceive.java:147)
	at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:62)
	at reactor.netty.ByteBufFlux.subscribe(ByteBufFlux.java:340)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4490)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onError(FluxOnAssembly.java:544)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onError(FluxMapFuseable.java:142)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onError(FluxContextWrite.java:121)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onError(FluxMapFuseable.java:340)
	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onError(FluxFilterFuseable.java:382)
	at reactor.core.publisher.MonoCollect$CollectSubscriber.onError(MonoCollect.java:145)
	at reactor.core.publisher.MonoCollect$CollectSubscriber.onNext(MonoCollect.java:124)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onNext(FluxPeek.java:200)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
	at reactor.netty.channel.FluxReceive.onInboundNext(FluxReceive.java:379)
	at reactor.netty.channel.ChannelOperations.onInboundNext(ChannelOperations.java:419)
	at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:790)
	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:114)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:103)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:333)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:454)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:290)
	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:829)
	Suppressed: java.lang.Exception: #block terminated with an error
		at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:100)
		at reactor.core.publisher.Mono.block(Mono.java:1742)
		at com.example.pure.service.impl.PureVideoUrlServiceImpl.downloadAndUploadVideoAsync(PureVideoUrlServiceImpl.java:516)
		at com.example.pure.service.impl.PureVideoUrlServiceImpl$$FastClassBySpringCGLIB$$6da7a9f9.invoke(<generated>)
		at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
		at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
		at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
		at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
		at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
		at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
		at org.springframework.security.concurrent.DelegatingSecurityContextRunnable.run(DelegatingSecurityContextRunnable.java:82)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
		... 1 common frames omitted
Caused by: org.springframework.core.io.buffer.DataBufferLimitException: Exceeded limit on max bytes to buffer : 16777216
	at org.springframework.core.io.buffer.LimitedDataBufferList.raiseLimitException(LimitedDataBufferList.java:99)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Body from GET http://p16-va-tiktok.ibyteimg.com/obj/tos-alisg-v-0051c001-sg/oIw8Liiq0A7lQokxABAk419BiVAEoEfmEBayMy [DefaultClientResponse]
Original Stack Trace:
		at org.springframework.core.io.buffer.LimitedDataBufferList.raiseLimitException(LimitedDataBufferList.java:99)
		at org.springframework.core.io.buffer.LimitedDataBufferList.updateCount(LimitedDataBufferList.java:92)
		at org.springframework.core.io.buffer.LimitedDataBufferList.add(LimitedDataBufferList.java:58)
		at reactor.core.publisher.MonoCollect$CollectSubscriber.onNext(MonoCollect.java:119)
		at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onNext(FluxPeek.java:200)
		at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
		at reactor.netty.channel.FluxReceive.onInboundNext(FluxReceive.java:379)
		at reactor.netty.channel.ChannelOperations.onInboundNext(ChannelOperations.java:419)
		at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:790)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:114)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:103)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:333)
		at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:454)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:290)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
		at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:829)
2025-06-29 00:48:33.909 [34mINFO [0;39m [file-async-1] c.e.p.s.impl.PureVideoUrlServiceImpl : 开始异步生成雪碧图 - videoId: 46, episode: 5
2025-06-29 00:48:33.910 [34mINFO [0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl : Executing command: ffmpeg -i http://p16-va-tiktok.ibyteimg.com/obj/tos-alisg-v-0051c001-sg/o0MdEZi19IomUUpSAXBAEBC9m90wQAcBXSikf4 -vf fps=1/3,scale=160:-1,tile=23x23 -q:v 2 -y d:\upload\video_sprites\46\output_5.jpg
2025-06-29 00:48:34.538 [39mDEBUG[0;39m [reactor-http-nio-7] o.s.w.r.f.client.ExchangeFunctions : [1daaee79] [a8fc289c-1] Response 200 OK
2025-06-29 00:48:34.887 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl : ffmpeg version 7.1.1-full_build-www.gyan.dev Copyright (c) 2000-2025 the FFmpeg developers
2025-06-29 00:48:34.887 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :   built with gcc 14.2.0 (Rev1, Built by MSYS2 project)
2025-06-29 00:48:34.887 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :   configuration: --enable-gpl --enable-version3 --enable-static --disable-w32threads --disable-autodetect --enable-fontconfig --enable-iconv --enable-gnutls --enable-lcms2 --enable-libxml2 --enable-gmp --enable-bzlib --enable-lzma --enable-libsnappy --enable-zlib --enable-librist --enable-libsrt --enable-libssh --enable-libzmq --enable-avisynth --enable-libbluray --enable-libcaca --enable-libdvdnav --enable-libdvdread --enable-sdl2 --enable-libaribb24 --enable-libaribcaption --enable-libdav1d --enable-libdavs2 --enable-libopenjpeg --enable-libquirc --enable-libuavs3d --enable-libxevd --enable-libzvbi --enable-libqrencode --enable-librav1e --enable-libsvtav1 --enable-libvvenc --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxavs2 --enable-libxeve --enable-libxvid --enable-libaom --enable-libjxl --enable-libvpx --enable-mediafoundation --enable-libass --enable-frei0r --enable-libfreetype --enable-libfribidi --enable-libharfbuzz --enable-liblensfun --enable-libvidstab --enable-libvmaf --enable-libzimg --enable-amf --enable-cuda-llvm --enable-cuvid --enable-dxva2 --enable-d3d11va --enable-d3d12va --enable-ffnvcodec --enable-libvpl --enable-nvdec --enable-nvenc --enable-vaapi --enable-libshaderc --enable-vulkan --enable-libplacebo --enable-opencl --enable-libcdio --enable-libgme --enable-libmodplug --enable-libopenmpt --enable-libopencore-amrwb --enable-libmp3lame --enable-libshine --enable-libtheora --enable-libtwolame --enable-libvo-amrwbenc --enable-libcodec2 --enable-libilbc --enable-libgsm --enable-liblc3 --enable-libopencore-amrnb --enable-libopus --enable-libspeex --enable-libvorbis --enable-ladspa --enable-libbs2b --enable-libflite --enable-libmysofa --enable-librubberband --enable-libsoxr --enable-chromaprint
2025-06-29 00:48:34.887 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :   libavutil      59. 39.100 / 59. 39.100
2025-06-29 00:48:34.887 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :   libavcodec     61. 19.101 / 61. 19.101
2025-06-29 00:48:34.887 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :   libavformat    61.  7.100 / 61.  7.100
2025-06-29 00:48:34.887 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :   libavdevice    61.  3.100 / 61.  3.100
2025-06-29 00:48:34.887 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :   libavfilter    10.  4.100 / 10.  4.100
2025-06-29 00:48:34.887 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :   libswscale      8.  3.100 /  8.  3.100
2025-06-29 00:48:34.887 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :   libswresample   5.  3.100 /  5.  3.100
2025-06-29 00:48:34.887 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :   libpostproc    58.  3.100 / 58.  3.100
2025-06-29 00:48:35.632 [1;31mERROR[0;39m [file-async-6] c.e.p.s.impl.PureVideoUrlServiceImpl : 异步下载并上传视频文件失败 - videoId: 46, episode: 5, 错误: 200 OK from GET http://p16-va-tiktok.ibyteimg.com/obj/tos-alisg-v-0051c001-sg/o0MdEZi19IomUUpSAXBAEBC9m90wQAcBXSikf4; nested exception is reactor.netty.http.client.PrematureCloseException: Connection prematurely closed DURING response
org.springframework.web.reactive.function.client.WebClientResponseException: 200 OK from GET http://p16-va-tiktok.ibyteimg.com/obj/tos-alisg-v-0051c001-sg/o0MdEZi19IomUUpSAXBAEBC9m90wQAcBXSikf4; nested exception is reactor.netty.http.client.PrematureCloseException: Connection prematurely closed DURING response
	at org.springframework.web.reactive.function.client.WebClientResponseException.create(WebClientResponseException.java:250)
	at org.springframework.web.reactive.function.client.DefaultClientResponse.lambda$createException$1(DefaultClientResponse.java:207)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:106)
	at reactor.core.publisher.FluxOnErrorReturn$ReturnSubscriber.onError(FluxOnErrorReturn.java:199)
	at reactor.core.publisher.Operators$MonoSubscriber.onError(Operators.java:1886)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onError(FluxOnAssembly.java:544)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onError(FluxMapFuseable.java:142)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onError(FluxContextWrite.java:121)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onError(FluxMapFuseable.java:340)
	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onError(FluxFilterFuseable.java:382)
	at reactor.core.publisher.MonoCollect$CollectSubscriber.onError(MonoCollect.java:145)
	at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
	at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
	at reactor.core.publisher.Operators.error(Operators.java:198)
	at reactor.netty.channel.FluxReceive.startReceiver(FluxReceive.java:177)
	at reactor.netty.channel.FluxReceive.subscribe(FluxReceive.java:147)
	at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:62)
	at reactor.netty.ByteBufFlux.subscribe(ByteBufFlux.java:340)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4490)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onError(FluxOnAssembly.java:544)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onError(FluxMapFuseable.java:142)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onError(FluxContextWrite.java:121)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onError(FluxMapFuseable.java:340)
	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onError(FluxFilterFuseable.java:382)
	at reactor.core.publisher.MonoCollect$CollectSubscriber.onError(MonoCollect.java:145)
	at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
	at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
	at reactor.netty.channel.FluxReceive.onInboundError(FluxReceive.java:465)
	at reactor.netty.channel.ChannelOperations.onInboundError(ChannelOperations.java:508)
	at reactor.netty.http.client.HttpClientOperations.onInboundClose(HttpClientOperations.java:320)
	at reactor.netty.channel.ChannelOperationsHandler.channelInactive(ChannelOperationsHandler.java:73)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:305)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:281)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:274)
	at io.netty.channel.ChannelInboundHandlerAdapter.channelInactive(ChannelInboundHandlerAdapter.java:81)
	at io.netty.handler.codec.http.HttpContentDecoder.channelInactive(HttpContentDecoder.java:235)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:305)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:281)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:274)
	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelInactive(CombinedChannelDuplexHandler.java:418)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInputClosed(ByteToMessageDecoder.java:411)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInactive(ByteToMessageDecoder.java:376)
	at io.netty.handler.codec.http.HttpClientCodec$Decoder.channelInactive(HttpClientCodec.java:328)
	at io.netty.channel.CombinedChannelDuplexHandler.channelInactive(CombinedChannelDuplexHandler.java:221)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:303)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:281)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:274)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelInactive(DefaultChannelPipeline.java:1405)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:301)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:281)
	at io.netty.channel.DefaultChannelPipeline.fireChannelInactive(DefaultChannelPipeline.java:901)
	at io.netty.channel.AbstractChannel$AbstractUnsafe$7.run(AbstractChannel.java:813)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasksFrom(SingleThreadEventExecutor.java:426)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:375)
	at io.netty.util.concurrent.SingleThreadEventExecutor.confirmShutdown(SingleThreadEventExecutor.java:763)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:596)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:829)
	Suppressed: java.lang.Exception: #block terminated with an error
		at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:100)
		at reactor.core.publisher.Mono.block(Mono.java:1742)
		at com.example.pure.service.impl.PureVideoUrlServiceImpl.downloadAndUploadVideoAsync(PureVideoUrlServiceImpl.java:516)
		at com.example.pure.service.impl.PureVideoUrlServiceImpl$$FastClassBySpringCGLIB$$6da7a9f9.invoke(<generated>)
		at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
		at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
		at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
		at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
		at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
		at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
		at org.springframework.security.concurrent.DelegatingSecurityContextRunnable.run(DelegatingSecurityContextRunnable.java:82)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
		... 1 common frames omitted
Caused by: reactor.netty.http.client.PrematureCloseException: Connection prematurely closed DURING response
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Body from GET http://p16-va-tiktok.ibyteimg.com/obj/tos-alisg-v-0051c001-sg/o0MdEZi19IomUUpSAXBAEBC9m90wQAcBXSikf4 [DefaultClientResponse]
	*__checkpoint ⇢ Body from GET http://p16-va-tiktok.ibyteimg.com/obj/tos-alisg-v-0051c001-sg/o0MdEZi19IomUUpSAXBAEBC9m90wQAcBXSikf4 [DefaultClientResponse]
Original Stack Trace:
2025-06-29 00:48:35.633 [34mINFO [0;39m [file-async-6] c.e.p.s.impl.PureVideoUrlServiceImpl : 开始异步下载并上传视频文件 - videoId: 46, episode: 6, url: http://p16-va-tiktok.ibyteimg.com/obj/tos-alisg-v-0051c001-sg/ooKghLKGIRRQAfOip1eREPdAb51kQGgADXeLCY
2025-06-29 00:48:35.633 [39mDEBUG[0;39m [file-async-6] o.s.w.r.f.client.ExchangeFunctions : [44e8e1fe] HTTP GET http://p16-va-tiktok.ibyteimg.com/obj/tos-alisg-v-0051c001-sg/ooKghLKGIRRQAfOip1eREPdAb51kQGgADXeLCY
2025-06-29 00:48:35.949 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl : [mov,mp4,m4a,3gp,3g2,mj2 @ 0000017b9cc9e880] stream 0, timescale not set
2025-06-29 00:48:35.974 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl : Input #0, mov,mp4,m4a,3gp,3g2,mj2, from 'http://p16-va-tiktok.ibyteimg.com/obj/tos-alisg-v-0051c001-sg/o0MdEZi19IomUUpSAXBAEBC9m90wQAcBXSikf4':
2025-06-29 00:48:35.974 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :   Metadata:
2025-06-29 00:48:35.974 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :     major_brand     : isom
2025-06-29 00:48:35.974 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :     minor_version   : 512
2025-06-29 00:48:35.974 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :     compatible_brands: isomiso2avc1mp41
2025-06-29 00:48:35.974 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :     title           : 記憶縫線 YOUR FORMA
2025-06-29 00:48:35.974 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :     album           : 記憶縫線 YOUR FORMA
2025-06-29 00:48:35.974 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :     encoder         : Lavf58.45.100
2025-06-29 00:48:35.974 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :     description     :  腦部用的縫線（記憶縫線），通稱「Your Forma（你的程式）」。這個在1992年爆發的病毒性腦炎大流行中救人於危難的醫療技術，如今已經進化為日常生活中不可或缺的大腦侵入型終端。看過的事
2025-06-29 00:48:35.974 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :   Duration: 00:23:45.07, start: 0.000000, bitrate: 2778 kb/s
2025-06-29 00:48:35.974 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :   Stream #0:0[0x2](und): Video: h264 (High) (avc1 / 0x31637661), yuv420p(progressive), 1920x1080 [SAR 1:1 DAR 16:9], 2457 kb/s, 23.98 fps, 23.98 tbr, 90k tbn (default)
2025-06-29 00:48:35.974 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :       Metadata:
2025-06-29 00:48:35.974 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :         handler_name    : VideoHandler
2025-06-29 00:48:35.974 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :         vendor_id       : [0][0][0][0]
2025-06-29 00:48:35.974 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :   Stream #0:1[0x3](jpn): Audio: aac (LC) (mp4a / 0x6134706D), 48000 Hz, stereo, fltp, 312 kb/s (default)
2025-06-29 00:48:35.974 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :       Metadata:
2025-06-29 00:48:35.974 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :         handler_name    : SoundHandler
2025-06-29 00:48:35.974 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :         vendor_id       : [0][0][0][0]
2025-06-29 00:48:35.974 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :   Stream #0:2[0x0]: Video: mjpeg (Baseline), yuvj420p(pc, bt470bg/unknown/unknown), 800x1130 [SAR 72:72 DAR 80:113], 90k tbr, 90k tbn (attached pic)
2025-06-29 00:48:36.090 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl : Stream mapping:
2025-06-29 00:48:36.090 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl :   Stream #0:0 -> #0:0 (h264 (native) -> mjpeg (native))
2025-06-29 00:48:36.091 [39mDEBUG[0;39m [file-async-1] c.e.p.service.impl.FFmpegServiceImpl : Press [q] to stop, [?] for help
2025-06-29 00:48:38.394 [34mINFO [0;39m [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown initiated...
2025-06-29 00:48:38.425 [34mINFO [0;39m [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown completed.
2025-06-29 00:51:06.760 [34mINFO [0;39m [main] com.example.pure.PureApplication : Starting PureApplication using Java 11.0.27 on DESKTOP-DQ33ANO with PID 18364 (C:\Users\<USER>\IdeaProjects\pure\target\classes started by Hao in C:\Users\<USER>\IdeaProjects\pure)
2025-06-29 00:51:06.764 [39mDEBUG[0;39m [main] com.example.pure.PureApplication : Running with Spring Boot v2.7.18, Spring v5.3.31
2025-06-29 00:51:06.765 [34mINFO [0;39m [main] com.example.pure.PureApplication : The following 1 profile is active: "dev"
2025-06-29 00:51:06.766 [34mINFO [0;39m [background-preinit] o.h.validator.internal.util.Version : HV000001: Hibernate Validator 6.2.5.Final
2025-06-29 00:51:10.115 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-29 00:51:10.121 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-29 00:51:10.286 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 77 ms. Found 0 Redis repository interfaces.
2025-06-29 00:51:10.673 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\AccessLogMapper.class]
2025-06-29 00:51:10.674 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\MessagesMapper.class]
2025-06-29 00:51:10.674 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OAuth2Mapper.class]
2025-06-29 00:51:10.674 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\OperatingLogMapper.class]
2025-06-29 00:51:10.675 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoEpisodesMapper.class]
2025-06-29 00:51:10.675 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInfoTypeLinkMapper.class]
2025-06-29 00:51:10.675 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoInteractionMapper.class]
2025-06-29 00:51:10.675 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoTypeMapper.class]
2025-06-29 00:51:10.675 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\PureVideoUrlMapper.class]
2025-06-29 00:51:10.675 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\RoleMapper.class]
2025-06-29 00:51:10.676 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserMapper.class]
2025-06-29 00:51:10.677 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserProfileMapper.class]
2025-06-29 00:51:10.677 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\UserRoleMapper.class]
2025-06-29 00:51:10.677 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\Users\<USER>\IdeaProjects\pure\target\classes\com\example\pure\mapper\primary\VideoCommentInteractionMapper.class]
2025-06-29 00:51:10.683 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-06-29 00:51:10.688 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-06-29 00:51:10.690 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'messagesMapper' and 'com.example.pure.mapper.primary.MessagesMapper' mapperInterface
2025-06-29 00:51:10.691 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'messagesMapper'.
2025-06-29 00:51:10.691 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'OAuth2Mapper' and 'com.example.pure.mapper.primary.OAuth2Mapper' mapperInterface
2025-06-29 00:51:10.692 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'OAuth2Mapper'.
2025-06-29 00:51:10.692 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'operatingLogMapper' and 'com.example.pure.mapper.primary.OperatingLogMapper' mapperInterface
2025-06-29 00:51:10.693 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'operatingLogMapper'.
2025-06-29 00:51:10.693 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoEpisodesMapper' and 'com.example.pure.mapper.primary.PureVideoEpisodesMapper' mapperInterface
2025-06-29 00:51:10.693 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoEpisodesMapper'.
2025-06-29 00:51:10.693 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper' and 'com.example.pure.mapper.primary.PureVideoInfoTypeLinkMapper' mapperInterface
2025-06-29 00:51:10.694 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper'.
2025-06-29 00:51:10.694 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInteractionMapper' and 'com.example.pure.mapper.primary.PureVideoInteractionMapper' mapperInterface
2025-06-29 00:51:10.695 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInteractionMapper'.
2025-06-29 00:51:10.695 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoTypeMapper' and 'com.example.pure.mapper.primary.PureVideoTypeMapper' mapperInterface
2025-06-29 00:51:10.695 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoTypeMapper'.
2025-06-29 00:51:10.695 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoUrlMapper' and 'com.example.pure.mapper.primary.PureVideoUrlMapper' mapperInterface
2025-06-29 00:51:10.696 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoUrlMapper'.
2025-06-29 00:51:10.696 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-06-29 00:51:10.696 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-06-29 00:51:10.696 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-06-29 00:51:10.697 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-06-29 00:51:10.697 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userProfileMapper' and 'com.example.pure.mapper.primary.UserProfileMapper' mapperInterface
2025-06-29 00:51:10.697 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userProfileMapper'.
2025-06-29 00:51:10.697 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userRoleMapper' and 'com.example.pure.mapper.primary.UserRoleMapper' mapperInterface
2025-06-29 00:51:10.697 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userRoleMapper'.
2025-06-29 00:51:10.697 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'videoCommentInteractionMapper' and 'com.example.pure.mapper.primary.VideoCommentInteractionMapper' mapperInterface
2025-06-29 00:51:10.698 [39mDEBUG[0;39m [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'videoCommentInteractionMapper'.
2025-06-29 00:51:12.093 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat initialized with port(s): 8080 (http)
2025-06-29 00:51:12.103 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol : Initializing ProtocolHandler ["http-nio-8080"]
2025-06-29 00:51:12.105 [34mINFO [0;39m [main] o.a.catalina.core.StandardService : Starting service [Tomcat]
2025-06-29 00:51:12.105 [34mINFO [0;39m [main] o.a.catalina.core.StandardEngine : Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-06-29 00:51:12.280 [34mINFO [0;39m [main] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring embedded WebApplicationContext
2025-06-29 00:51:12.280 [34mINFO [0;39m [main] o.s.b.w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 5433 ms
2025-06-29 00:51:12.799 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\AccessLogMapper.xml]'
2025-06-29 00:51:12.818 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\MessagesMapper.xml]'
2025-06-29 00:51:12.826 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OAuth2Mapper.xml]'
2025-06-29 00:51:12.836 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\OperatingLogMapper.xml]'
2025-06-29 00:51:12.854 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoEpisodesMapper.xml]'
2025-06-29 00:51:12.862 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInfoTypeLinkMapper.xml]'
2025-06-29 00:51:12.875 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoInteractionMapper.xml]'
2025-06-29 00:51:12.884 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoTypeMapper.xml]'
2025-06-29 00:51:12.896 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\PureVideoUrlMapper.xml]'
2025-06-29 00:51:12.923 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\RoleMapper.xml]'
2025-06-29 00:51:12.947 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserMapper.xml]'
2025-06-29 00:51:12.967 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserProfileMapper.xml]'
2025-06-29 00:51:12.983 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\UserRoleMapper.xml]'
2025-06-29 00:51:12.998 [39mDEBUG[0;39m [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\Users\<USER>\IdeaProjects\pure\target\classes\mapper\VideoCommentInteractionMapper.xml]'
2025-06-29 00:51:13.043 [34mINFO [0;39m [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Starting...
2025-06-29 00:51:13.994 [34mINFO [0;39m [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Start completed.
2025-06-29 00:51:14.771 [39mDEBUG[0;39m [main] o.s.w.s.r.ResourceUrlEncodingFilter : Filter 'resourceUrlEncodingFilter' configured for use
2025-06-29 00:51:14.772 [39mDEBUG[0;39m [main] com.example.pure.filter.JwtFilter : Filter 'jwtFilter' configured for use
2025-06-29 00:51:15.141 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.PageRequestDTO)
2025-06-29 00:51:15.144 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.OperatingLogController; public com.example.pure.common.Result com.example.pure.controller.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.PageRequestDTO)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-29 00:51:15.922 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#id)") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-06-29 00:51:15.924 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-06-29 00:51:15.925 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUsersByPage(com.example.pure.model.dto.PageRequestDTO)
2025-06-29 00:51:15.925 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUsersByPage(com.example.pure.model.dto.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-29 00:51:15.926 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-06-29 00:51:15.926 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-06-29 00:51:15.926 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getAllUsers()
2025-06-29 00:51:15.926 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-29 00:51:15.926 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)
2025-06-29 00:51:15.926 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasRole('ADMIN') or isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-06-29 00:51:15.927 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserController.deleteUser(java.lang.Long)
2025-06-29 00:51:15.927 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserController; public com.example.pure.common.Result com.example.pure.controller.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-29 00:51:15.932 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasAnyRole('COMMON', 'ADMIN')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)
2025-06-29 00:51:15.932 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-06-29 00:51:15.933 [39mDEBUG[0;39m [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize(value="hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())") found on specific method: public com.example.pure.common.Result com.example.pure.controller.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)
2025-06-29 00:51:15.933 [39mDEBUG[0;39m [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())', filter: 'null', filterTarget: 'null']]
2025-06-29 00:51:16.059 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-06-29 00:51:16.061 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-06-29 00:51:16.066 [34mINFO [0;39m [main] o.s.s.web.DefaultSecurityFilterChain : Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@3d0697da, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4b99f2ed, org.springframework.security.web.context.SecurityContextPersistenceFilter@19b4dd60, org.springframework.security.web.header.HeaderWriterFilter@7b9f7087, org.springframework.security.web.authentication.logout.LogoutFilter@511ad0dd, com.example.pure.filter.JwtFilter@4fc256ec, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@4138af7, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5bbf3869, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@723081a2, org.springframework.security.web.session.SessionManagementFilter@59301546, org.springframework.security.web.access.ExceptionTranslationFilter@51ac72f7, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@1a3e8b68]
2025-06-29 00:51:16.068 [34mINFO [0;39m [main] com.example.pure.config.AsyncConfig : 创建文件操作异步任务线程池
2025-06-29 00:51:16.070 [34mINFO [0;39m [main] com.example.pure.config.AsyncConfig : 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-06-29 00:51:16.257 [39mDEBUG[0;39m [main] o.s.w.s.s.s.WebSocketHandlerMapping : Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-06-29 00:51:16.278 [34mINFO [0;39m [main] o.s.b.a.w.s.WelcomePageHandlerMapping : Adding welcome page: class path resource [static/index.html]
2025-06-29 00:51:16.341 [39mDEBUG[0;39m [main] o.s.w.s.m.m.a.RequestMappingHandlerMapping : 83 mappings in 'requestMappingHandlerMapping'
2025-06-29 00:51:16.350 [39mDEBUG[0;39m [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-06-29 00:51:16.763 [39mDEBUG[0;39m [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-06-29 00:51:16.956 [34mINFO [0;39m [main] o.s.b.a.e.web.EndpointLinksResolver : Exposing 16 endpoint(s) beneath base path '/actuator'
2025-06-29 00:51:16.989 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-06-29 00:51:16.989 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/password']
2025-06-29 00:51:16.990 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/createUser']
2025-06-29 00:51:16.990 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/refresh/**']
2025-06-29 00:51:16.990 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/captcha']
2025-06-29 00:51:16.990 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-06-29 00:51:16.990 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-06-29 00:51:16.990 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/**']
2025-06-29 00:51:16.990 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-06-29 00:51:16.990 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws-raw/**']
2025-06-29 00:51:16.990 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/render/**']
2025-06-29 00:51:16.990 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/callback/**']
2025-06-29 00:51:16.990 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/refresh/**']
2025-06-29 00:51:16.990 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/verification/**']
2025-06-29 00:51:16.990 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/update/password']
2025-06-29 00:51:16.990 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/videoUrl/**']
2025-06-29 00:51:16.990 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video-legacy/**']
2025-06-29 00:51:16.990 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image/**']
2025-06-29 00:51:16.991 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image-legacy/**']
2025-06-29 00:51:16.991 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download/**']
2025-06-29 00:51:16.991 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download-legacy/**']
2025-06-29 00:51:16.991 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-06-29 00:51:16.991 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-06-29 00:51:16.991 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-06-29 00:51:16.991 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-06-29 00:51:16.991 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-06-29 00:51:16.991 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-06-29 00:51:16.991 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-06-29 00:51:16.991 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-06-29 00:51:16.991 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-06-29 00:51:16.991 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-06-29 00:51:16.991 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-06-29 00:51:16.991 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-06-29 00:51:16.991 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/openapi.json']
2025-06-29 00:51:16.991 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v2/**']
2025-06-29 00:51:16.991 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-06-29 00:51:16.991 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/user/get/**']
2025-06-29 00:51:16.991 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/profile/**']
2025-06-29 00:51:16.991 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-06-29 00:51:16.991 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-06-29 00:51:16.992 [39mDEBUG[0;39m [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [authenticated] for any request
2025-06-29 00:51:16.992 [34mINFO [0;39m [main] o.s.s.web.DefaultSecurityFilterChain : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@43e6f05f, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2fccfcdb, org.springframework.security.web.context.SecurityContextPersistenceFilter@1067192a, org.springframework.security.web.header.HeaderWriterFilter@5ce03a9d, org.springframework.web.filter.CorsFilter@6c04310f, org.springframework.security.web.authentication.logout.LogoutFilter@6915351c, com.example.pure.filter.JwtFilter@4fc256ec, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@3f7f9d3f, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@62966c9f, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5ce7ab6f, org.springframework.security.web.session.SessionManagementFilter@10c67c1c, org.springframework.security.web.access.ExceptionTranslationFilter@5c3e7128, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@23f27434]
2025-06-29 00:51:17.025 [39mTRACE[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : Looking for @MessageExceptionHandler mappings: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@7b02e036, started on Sun Jun 29 00:51:06 CST 2025
2025-06-29 00:51:17.037 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.AuthController:
	
2025-06-29 00:51:17.037 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.DownloadController:
	
2025-06-29 00:51:17.037 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.FileManagerController:
	
2025-06-29 00:51:17.037 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.ImageController:
	
2025-06-29 00:51:17.038 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.MessagesController:
	
2025-06-29 00:51:17.038 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.OAuth2Controller:
	
2025-06-29 00:51:17.038 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.OperatingLogController:
	
2025-06-29 00:51:17.038 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureFileManagerController:
	
2025-06-29 00:51:17.038 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureImageFileController:
	
2025-06-29 00:51:17.038 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureVideoController:
	
2025-06-29 00:51:17.038 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureVideoInteractionController:
	
2025-06-29 00:51:17.038 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.PureVideoUrlController:
	
2025-06-29 00:51:17.038 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.QRCodeController:
	
2025-06-29 00:51:17.041 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.QRLoginController:
	{[MESSAGE],[/qrlogin/subscribe]}: subscribeQRStatus(String)
2025-06-29 00:51:17.041 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.R2Controller:
	
2025-06-29 00:51:17.041 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.UserController:
	
2025-06-29 00:51:17.041 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.UserProfileController:
	
2025-06-29 00:51:17.041 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.VerificationController:
	
2025-06-29 00:51:17.041 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.VideoCommentInteractionController:
	
2025-06-29 00:51:17.041 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.VideoController:
	
2025-06-29 00:51:17.041 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.WebSocketTestController:
	{[MESSAGE],[/echo]}: echo(String)
	{[MESSAGE],[/chat]}: handleChatMessage(WebSocketMessage)
	{[MESSAGE],[/room/{roomId}]}: sendToRoom(String,Map)
	{[MESSAGE],[/message]}: broadcastMessage(Map)
	{[MESSAGE],[/private-message]}: privateMessage(Map,SimpMessageHeaderAccessor,Principal)
2025-06-29 00:51:17.042 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-06-29 00:51:17.043 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-06-29 00:51:17.043 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-06-29 00:51:17.043 [39mDEBUG[0;39m [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerConfigResource:
	
2025-06-29 00:51:17.127 [39mDEBUG[0;39m [main] o.s.w.s.m.m.a.RequestMappingHandlerAdapter : ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-06-29 00:51:17.164 [39mDEBUG[0;39m [main] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-06-29 00:51:17.551 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol : Starting ProtocolHandler ["http-nio-8080"]
2025-06-29 00:51:17.562 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat started on port(s): 8080 (http) with context path ''
2025-06-29 00:51:17.564 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-06-29 00:51:17.565 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-06-29 00:51:17.565 [34mINFO [0;39m [main] o.s.m.s.b.SimpleBrokerMessageHandler : Starting...
2025-06-29 00:51:17.565 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@47fb30ca]
2025-06-29 00:51:17.565 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@47fb30ca]
2025-06-29 00:51:17.565 [34mINFO [0;39m [main] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@47fb30ca]]
2025-06-29 00:51:17.565 [34mINFO [0;39m [main] o.s.m.s.b.SimpleBrokerMessageHandler : Started.
2025-06-29 00:51:17.565 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-29 00:51:17.565 [39mDEBUG[0;39m [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-06-29 00:51:17.580 [34mINFO [0;39m [main] com.example.pure.PureApplication : Started PureApplication in 11.721 seconds (JVM running for 13.704)
2025-06-29 00:52:17.020 [34mINFO [0;39m [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
