package com.example.pure.model.dto;

import lombok.Data;

import java.time.Instant;

/**
 * 视频分集公开DTO - 用于前端展示
 * <p>
 * 此DTO不包含敏感的objectKey字段，只包含前端需要的数据
 * 适用于需要完全隔离敏感数据的场景
 * </p>
 */
@Data
public class VideoEpisodesPublicDTO {

    Long id;
    Long videoInfoId;
    String number;
    /**
     * 临时播放URL，由R2预签名URL生成，用于前端播放视频
     */
    String playUrl;
    String duration;
    /**
     * 雪碧图的临时访问URL
     */
    String spriteSheetUrl;
    Instant createdTime;
    Instant updatedTime;
    Long likesCount;
    /**
     * 当前登录用户是否已点赞此分集
     * true: 已点赞, false: 未点赞/未登录
     */
    Boolean status = false;
}
