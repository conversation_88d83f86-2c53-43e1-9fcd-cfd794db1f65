package com.example.pure.service.impl;

import com.example.pure.common.PageFinalResult;
import com.example.pure.common.PageResult;
import com.example.pure.constant.SecurityConstants;
import com.example.pure.exception.BusinessException;
import com.example.pure.mapper.primary.*;
import com.example.pure.model.dto.EpisodeLikeCountDTO;
import com.example.pure.model.dto.PageRequestDTO;
import com.example.pure.model.dto.VideoEpisodesDTO;
import com.example.pure.model.dto.VideoInfoWithEpisodesDto;
import com.example.pure.model.entity.VideoInfo;
import com.example.pure.model.entity.VideoInfoTypeLink;
import com.example.pure.model.entity.VideoType;
import com.example.pure.service.FFmpegService;
import com.example.pure.service.PureVideoUrlService;
import com.example.pure.service.R2Service;
import com.example.pure.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.reactive.function.client.WebClient;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 视频上传服务实现类
 */
@Service
@Slf4j
public class PureVideoUrlServiceImpl implements PureVideoUrlService {

    private final PureVideoUrlMapper pureUrlMapper;
    private final PureVideoEpisodesMapper pureUploadEpisodesMapper;
    private final PureVideoTypeMapper pureVideoTypeMapper;
    private final PureVideoInfoTypeLinkMapper pureVideoInfoTypeLinkMapper;
    private final PureVideoInteractionMapper pureVideoInteractionMapper;
    private final FFmpegService ffmpegService;
    private final WebClient webClient;
    private final R2Service r2Service;
    private final RedisUtil redisUtil;

    // 注入自身代理以解决@Async自调用问题
    @Autowired
    @Lazy
    private PureVideoUrlService self;

    @Autowired
    public PureVideoUrlServiceImpl(
            PureVideoUrlMapper pureUrlMapper,
            PureVideoEpisodesMapper pureUploadEpisodesMapper,
            PureVideoTypeMapper pureVideoTypeMapper,
            PureVideoInfoTypeLinkMapper pureVideoInfoTypeLinkMapper,
            PureVideoInteractionMapper pureVideoInteractionMapper,
            FFmpegService ffmpegService,
            @Qualifier("directWebClient") WebClient webClient,
            R2Service r2Service,
            RedisUtil redisUtil) {
        this.pureUrlMapper = pureUrlMapper;
        this.pureUploadEpisodesMapper = pureUploadEpisodesMapper;
        this.pureVideoTypeMapper = pureVideoTypeMapper;
        this.pureVideoInfoTypeLinkMapper = pureVideoInfoTypeLinkMapper;
        this.pureVideoInteractionMapper = pureVideoInteractionMapper;
        this.ffmpegService = ffmpegService;
        this.webClient = webClient;
        this.r2Service = r2Service;
        this.redisUtil = redisUtil;
    }

    /**
     * 上传视频及其分集信息
     * @param videoUploadRequest 视频上传请求对象
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void uploadVideoWithEpisodes(VideoInfoWithEpisodesDto videoUploadRequest) {
        if (videoUploadRequest == null) {
            throw new BusinessException("上传视频失败：缺少视频信息");
        }

        // 1. 创建视频基本信息
        VideoInfo videoInfo = createVideoInfo(videoUploadRequest);

        // 2. 处理视频类型关联
        processVideoTypes(videoInfo.getId(), videoUploadRequest.getVideoTypes());

        // 3. 处理视频分集信息
        processVideoEpisodes(videoInfo.getId(), videoUploadRequest.getVideoEpisodes());
    }

    // 更新视频服务，如果视频已存在，就要更新的列表数量大于数据库已有的分集数量，才更新
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void uploadVideoEpisodes(VideoInfoWithEpisodesDto videoUploadRequest){
        if (videoUploadRequest == null) {
            throw new BusinessException("上传视频失败：缺少视频信息");
        }
        VideoInfo videoInfo=pureUrlMapper.findVideoInfoByTitle(videoUploadRequest.getTitle());

        if (videoInfo==null) {
                throw new BusinessException("查找不到视频信息");
        }
        // 获取上传的视频分集信息
        List<VideoEpisodesDTO> newEpisodes = videoUploadRequest.getVideoEpisodes();
        // 获取数据库的视频分集信息（使用基础查询避免复杂JOIN）
        List<VideoEpisodesDTO> existingEpisodes = pureUploadEpisodesMapper.findBasicVideoEpisodesByVideoInfoId(videoInfo.getId());


        // 1. 统计现有有效集数(有效集数要保留)中id和spriteSheetObjectKey都不为空的数量
        List<Long> idsToKeep = new ArrayList<>();
        for (VideoEpisodesDTO existing : existingEpisodes) {
            if (existing.getId() != null &&
                    existing.getSpriteSheetObjectKey() != null &&
                    !existing.getSpriteSheetObjectKey().trim().isEmpty()) {
                idsToKeep.add(existing.getId());
            }
        }

        // 2. 检查新集数是否大于现有有效集数
        if (newEpisodes.size() > idsToKeep.size()) {

            // 3. 找出需要删除的集数ID
            List<Long> idsToDelete = new ArrayList<>();
            for (VideoEpisodesDTO existing : existingEpisodes) {
                if (existing.getId() != null && !idsToKeep.contains(existing.getId())) {
                    idsToDelete.add(existing.getId());
                }
            }

            // 5. 删除不需要保留的集数
            if (!idsToDelete.isEmpty()) {
                int deletedEpisodes = pureUploadEpisodesMapper.deleteVideoEpisodesByIds(idsToDelete);
                if (deletedEpisodes <= 0) {
                    throw new BusinessException("删除视频分集信息失败，视频ID: " + videoInfo.getId());
                }
                log.info("删除了{}个旧分集，视频ID: {}", deletedEpisodes, videoInfo.getId());
            }

            // 6. 处理新的分集信息（只更新不在保留列表中的）
            List<VideoEpisodesDTO> episodesToProcess = new ArrayList<>();
            for (VideoEpisodesDTO newEpisode : newEpisodes) {
                // 如果新分集的ID不在保留列表中，则需要处理
                if (newEpisode.getId() == null || !idsToKeep.contains(newEpisode.getId())) {
                    episodesToProcess.add(newEpisode);
                }
            }

            // 7. 插入新的分集信息
            if (!episodesToProcess.isEmpty()) {
                processVideoEpisodes(videoInfo.getId(), episodesToProcess);
                log.info("处理了{}个新分集，视频ID: {}", episodesToProcess.size(), videoInfo.getId());
            }

        } else {
            throw new BusinessException("视频数量(" + newEpisodes.size() + ")不大于当前数据库有效数量(" + idsToKeep.size() + ")");
        }

    }

    /**
     * 获取视频的详细信息，包括所有分集以及当前用户的点赞状态。
     * <p>
     * 高性能重构方案：拆分查询避免复杂JOIN+聚合，提升高并发性能
     * 1. 先查询基础分集信息（避免JOIN）
     * 2. 批量查询点赞数据（避免复杂聚合）
     * 3. 组装数据并生成临时URL
     * </p>
     *
     * @param title  视频标题
     * @param userId 当前登录用户的ID (如果未登录，则为null)
     * @return 包含视频完整信息的DTO
     */
    @Override
    public VideoInfoWithEpisodesDto getVideoInfoWithEpisodes(String title, Long userId){
        // 获取视频信息
        VideoInfo videoInfo=pureUrlMapper.findVideoInfoByTitle(title);
        if (videoInfo==null) {
            throw new BusinessException("查找不到视频信息");
        }

        // 高性能方案：拆分查询
        List<VideoEpisodesDTO> videoEpisodes = getVideoEpisodesWithLikesOptimized(videoInfo.getId(), userId);

        // 为每个分集生成临时访问URL，使用Redis缓存提高性能
        generateTemporaryUrlsForEpisodes(videoEpisodes, videoInfo.getId());

        VideoInfoWithEpisodesDto videoUploadDto=new VideoInfoWithEpisodesDto();
        videoUploadDto.setTitle(videoInfo.getTitle());
        videoUploadDto.setDescription(videoInfo.getDescription());
        videoUploadDto.setCoverImageUrl(videoInfo.getCoverImageUrl());
        videoUploadDto.setVideoEpisodes(videoEpisodes);
        videoUploadDto.setVideoTypes(pureVideoTypeMapper.findVideoTypeByLinkWithVideoId(videoInfo.getId()));
        return videoUploadDto;
    }

    /**
     * 高性能获取分集信息（拆分查询方案）
     * <p>
     * 性能优化策略：
     * 1. 避免复杂JOIN：先查基础数据
     * 2. 批量查询点赞：使用IN查询代替聚合
     * 3. 内存组装：在应用层组装数据
     * </p>
     */
    private List<VideoEpisodesDTO> getVideoEpisodesWithLikesOptimized(Long videoInfoId, Long userId) {
        // 1. 查询基础分集信息（无JOIN，性能最优）
        List<VideoEpisodesDTO> episodes = pureUploadEpisodesMapper.findBasicVideoEpisodesByVideoInfoId(videoInfoId);

        if (ObjectUtils.isEmpty(episodes)) {
            return episodes;
        }

        // 2. 提取分集ID列表
        List<Long> episodeIds = episodes.stream()
                .map(VideoEpisodesDTO::getId)
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);

        // 3. 批量查询点赞数据（避免复杂聚合）
        Map<Long, Long> likesCountMap = getLikesCountMap(episodeIds);

        Set<Long> userLikedEpisodeIds = getUserLikedEpisodeIds(episodeIds, userId);

        // 4. 在内存中组装数据（避免数据库层复杂计算）
        episodes.forEach(episode -> {
            // 设置点赞数
            episode.setLikesCount(likesCountMap.getOrDefault(episode.getId(), 0L));
            // 设置用户点赞状态
            episode.setStatus(userLikedEpisodeIds.contains(episode.getId()));
        });

        return episodes;
    }

    /**
     * 获取分集点赞数量映射
     */
    private Map<Long, Long> getLikesCountMap(List<Long> episodeIds) {
        if (ObjectUtils.isEmpty(episodeIds)) {
            return Collections.emptyMap();
        }

        List<EpisodeLikeCountDTO> likeCounts = pureUploadEpisodesMapper.findLikesCountByEpisodeIds(episodeIds);
        return likeCounts.stream()
                .collect(Collectors.toMap(
                    EpisodeLikeCountDTO::getEpisodeId,
                    EpisodeLikeCountDTO::getLikesCount,
                    (existing, replacement) -> existing // 处理重复key
                ));
    }

    /**
     * 获取用户已点赞的分集ID集合
     */
    private Set<Long> getUserLikedEpisodeIds(List<Long> episodeIds, Long userId) {
        if (ObjectUtils.isEmpty(episodeIds) || userId == null) {
            return Collections.emptySet();
        }

        List<Long> likedIds = pureUploadEpisodesMapper.findUserLikedEpisodeIds(episodeIds, userId);
        return new HashSet<>(likedIds);
    }


    /**
     * 创建视频基本信息
     * @param request 视频上传请求
     * @return 创建后的视频信息对象
     */
    private VideoInfo createVideoInfo(VideoInfoWithEpisodesDto request) {
        // 创建视频信息
        VideoInfo videoInfo = new VideoInfo();
        videoInfo.setTitle(request.getTitle());
        videoInfo.setDescription(request.getDescription());
        videoInfo.setCoverImageUrl(request.getCoverImageUrl());

        //预防重新上传重复视频
        VideoInfo existingVideoInfo=pureUrlMapper.findVideoInfoByTitle(videoInfo.getTitle());
        if (existingVideoInfo!=null){
            throw new BusinessException("视频已存在，请别重新上传");
        }

        // 插入视频信息
        int videoInfoInsertedRows = pureUrlMapper.insertVideoInfo(videoInfo);
        if (videoInfoInsertedRows <= 0) {
            log.error("插入视频{}信息失败", videoInfo.getTitle());
            throw new BusinessException("上传视频失败：插入视频信息失败");
        }
        log.info("插入视频信息成功：{}", videoInfo.getTitle());

        // 查询刚插入的视频信息
        VideoInfo createdVideoInfo = pureUrlMapper.findVideoInfoByTitle(videoInfo.getTitle());
        if (createdVideoInfo == null) {
            throw new BusinessException("上传视频失败：无法获取刚创建的视频信息");
        }

        return createdVideoInfo;
    }

    /**
     * 处理视频类型关联
     * @param videoId 视频ID
     * @param videoTypes 视频类型列表
     */
    private void processVideoTypes(Long videoId, List<VideoType> videoTypes) {
        // 验证视频类型列表
        if (ObjectUtils.isEmpty(videoTypes)) {
            throw new BusinessException("上传视频失败：缺少视频类型");
        }

        // 创建视频信息和类型连接列表
        List<VideoInfoTypeLink> linksToCreate = new ArrayList<>();

        for (VideoType videoType : videoTypes) {
            if (videoType == null || videoType.getName() == null) {
                continue;
            }

            // 从数据库验证是否有这个类型
            VideoType existingVideoType = pureVideoTypeMapper.findVideoTypeByName(videoType.getName());
            Long typeId;

            // 处理类型不存在的情况
            if (existingVideoType == null || existingVideoType.getId() == null) {
                // 插入新类型
                int insertedRows = pureVideoTypeMapper.insertVideoType(videoType);
                if (insertedRows <= 0) {
                    throw new BusinessException("上传视频失败：插入视频类型失败");
                }

                // 重新查询获取ID
                existingVideoType = pureVideoTypeMapper.findVideoTypeByName(videoType.getName());
                if (existingVideoType == null || existingVideoType.getId() == null) {
                    throw new BusinessException("上传视频失败：插入视频类型后无法获取类型ID");
                }
            }

            typeId = existingVideoType.getId();

            // 创建视频信息和类型连接
            VideoInfoTypeLink link = new VideoInfoTypeLink();
            link.setVideoInfoId(videoId);
            link.setTypeId(typeId);
            linksToCreate.add(link);
        }

        // 如果有类型关联需要创建
        if (!linksToCreate.isEmpty()) {
            int linkInsertedRows = pureVideoInfoTypeLinkMapper.insertVideoInfoTypeLink(linksToCreate);
            if (linkInsertedRows <= 0) {
                log.error("插入视频类型关联信息失败，视频ID: {}", videoId);
                throw new BusinessException("上传视频失败：插入视频类型关联信息失败");
            }
            log.info("插入视频类型关联信息成功，视频ID: {}", videoId);
        }
    }

    /**
     * 处理视频分集信息
     * @param videoId 视频ID
     * @param episodes 分集列表
     */
    private void processVideoEpisodes(Long videoId, List<VideoEpisodesDTO> episodes) {
        // 验证分集列表
        if (ObjectUtils.isEmpty(episodes)) {
            log.warn("视频没有提供分集信息，视频ID: {}", videoId);
            return;
        }

        // 为本次处理的所有剧集生成一个共享的UUID
        String folderUuid = UUID.randomUUID().toString();

        // 为每个分集设置视频ID
        List<VideoEpisodesDTO> episodeList = new ArrayList<>();
        for (VideoEpisodesDTO episode : episodes) {
            if (ObjectUtils.isEmpty(episode)) {
                continue;
            }

            // 设置关联的视频ID
            episode.setVideoInfoId(videoId);

            // 如果episode有playUrl，先异步下载并上传到R2
            if (StringUtils.hasText(episode.getPlayUrl())) {
                try {
                    // 保存原始URL用于后续处理（此时objectKey字段存储的是原始下载URL）
                    String originalUrl = episode.getPlayUrl();

                    // self方法有@Async注解，主线程跳过这些方法使用线程池调用其他线程来运行
                    // 异步下载视频文件并上传到R2
                    self.downloadAndUploadVideoAsync(episode, videoId, folderUuid);

                    // 异步生成并上传雪碧图（使用原始URL，因为此时episode的playUrl可能还未更新）
                    // 使用代理对象调用，确保@Async生效
                    // 主线程跳过个有@Async线程池的代码，使用线程池里的其中一个线程去执行有@Async注解的方法
                    self.generateSpriteAsync(originalUrl, videoId, episode.getNumber(), folderUuid);
                } catch (Exception e) {
                    log.error("处理视频分集失败 - videoId: {}, episode: {}, 错误: {}", videoId, episode.getNumber(), e.getMessage(), e);
                    // 如果下载上传失败，继续处理其他分集，但记录错误
                }
            }
            // 主线程接着执行这个任务
            episodeList.add(episode);
        }

        // 如果有有效的分集数据，则插入数据库
        if (!ObjectUtils.isEmpty(episodeList)) {
            int episodesInsertedRows = pureUploadEpisodesMapper.insertVideoEpisodes(episodeList);
            if (episodesInsertedRows <= 0) {
                log.error("插入视频分集信息失败，视频ID: {}", videoId);
                throw new BusinessException("上传视频失败：插入视频分集信息失败");
            }
            log.info("插入视频分集信息成功，共{}集，视频ID: {}", episodeList.size(), videoId);
        }
    }

    // 获取视频信息按分页获取
    @Override
    public PageFinalResult<VideoInfo>  getVideoInfoWithPagination(PageRequestDTO pageRequest){
        // 计算分页参数
        int offset=pageRequest.getOffset();
        int limit=pageRequest.getPageSize();
        String keyword=pageRequest.getKeyword();

        List<VideoInfo> videoInfoList = pureUrlMapper.findVideoInfoWithPagination(offset,limit,keyword);
        // 获取视频总数
        int total=pureUrlMapper.countVideoInfo(keyword);
        PageResult pageResult= PageResult.of(pageRequest.getPageNum(),pageRequest.getPageSize(),total);
        PageFinalResult<VideoInfo> pageFinalResult=new PageFinalResult<>(videoInfoList,pageResult);
        return pageFinalResult;

    }

    // 获取视频的信息通过类型查询获取按分页查询
    @Override
    public  PageFinalResult<VideoInfo> getVideoInfoByTypeWithPagination(PageRequestDTO pageRequest) {
        // 计算分页参数
        int offset = pageRequest.getOffset();
        int limit = pageRequest.getPageSize();
        String keyword = pageRequest.getKeyword();
        if (!StringUtils.hasText(keyword)) {
            throw new BusinessException("请填写类型的关键字后再查询");
        }


        List<VideoInfo> videoInfoList = pureUrlMapper.findVideoInfoByTypeWithPagination(offset, limit, keyword);

        // 获取视频总数
        int total = pureUrlMapper.countVideoInfoByType(keyword);
        PageResult pageResult = PageResult.of(pageRequest.getPageNum(), pageRequest.getPageSize(), total);
        PageFinalResult<VideoInfo> pageFinalResult = new PageFinalResult<>(videoInfoList, pageResult);
        return pageFinalResult;
    }

    @Override
    public List<VideoType> getAllVideoType(){
         List<VideoType> allVideoType=pureVideoTypeMapper.findAllVideoType();
         return allVideoType;
    }

    @Override
    @Async("fileTaskExecutor")
    public void generateSpriteAsync(String playUrl, Long videoId, String episodeNumber, String folderUuid) {
        try {
            log.info("开始异步生成雪碧图 - videoId: {}, episode: {}", videoId, episodeNumber);
            String spriteObjectKey = ffmpegService.generateAndUploadSprite(playUrl, videoId, episodeNumber, folderUuid);

            if (StringUtils.hasText(spriteObjectKey)) {
                // 异步更新数据库中的雪碧图对象键
                int updatedRows = pureUploadEpisodesMapper.updateSpriteSheetObjectKey(videoId, episodeNumber, spriteObjectKey);
                if (updatedRows > 0) {
                    log.info("异步生成雪碧图成功并已更新数据库 - videoId: {}, episode: {}, objectKey: {}", videoId, episodeNumber, spriteObjectKey);
                } else {
                    log.warn("异步生成雪碧图成功但更新数据库失败 - videoId: {}, episode: {}, objectKey: {}", videoId, episodeNumber, spriteObjectKey);
                }
            } else {
                log.warn("异步生成雪碧图返回空objectKey - videoId: {}, episode: {}", videoId, episodeNumber);
            }
        } catch (Exception e) {
            // 如果雪碧图生成失败，仅记录错误，不影响主流程
            log.error("异步生成雪碧图失败 - videoId: {}, episode: {}, 错误: {}", videoId, episodeNumber, e.getMessage(), e);
        }
    }

    /**
     * 异步下载视频文件并上传到R2存储
     * <p>
     * 使用WebClient下载视频文件，然后上传到R2存储，最后更新episode的playUrl为R2的objectKey
     * </p>
     *
     * @param episode    视频分集DTO对象
     * @param videoId    视频ID
     * @param folderUuid 文件夹UUID，用于组织文件结构
     */
    @Override
    @Async("fileTaskExecutor")
    public void downloadAndUploadVideoAsync(VideoEpisodesDTO episode, Long videoId, String folderUuid) {
        try {
            log.info("开始异步下载并上传视频文件 - videoId: {}, episode: {}, url: {}",
                    videoId, episode.getNumber(), episode.getPlayUrl());

            // 1. 使用WebClient下载视频文件（此时playUrl存储的是原始下载URL）
            byte[] videoData = webClient.get()
                    .uri(episode.getPlayUrl())
                    .accept(MediaType.APPLICATION_OCTET_STREAM)
                    .retrieve()
                    .bodyToMono(byte[].class)
                    .block(); // 阻塞等待下载完成

            if (videoData == null || videoData.length == 0) {
                log.warn("下载的视频文件为空 - videoId: {}, episode: {}", videoId, episode.getNumber());
                return;
            }

            log.info("视频文件下载完成 - videoId: {}, episode: {}, 文件大小: {} bytes",
                    videoId, episode.getNumber(), videoData.length);

            // 2. 上传到R2存储
            String objectKey = r2Service.uploadVideoAssetFromBytes(videoData, folderUuid, episode.getNumber(), "video/mp4");

            log.info("视频文件上传到R2成功 - videoId: {}, episode: {}, objectKey: {}",
                    videoId, episode.getNumber(), objectKey);

            // 4. 异步更新数据库中的objectKey
            int updatedRows = pureUploadEpisodesMapper.updateEpisodeObjectKey(videoId, episode.getNumber(), objectKey);
            if (updatedRows > 0) {
                log.info("异步更新数据库objectKey成功 - videoId: {}, episode: {}, objectKey: {}",
                        videoId, episode.getNumber(), objectKey);
            } else {
                log.warn("异步更新数据库objectKey失败 - videoId: {}, episode: {}, objectKey: {}",
                        videoId, episode.getNumber(), objectKey);
            }

        } catch (IOException e) {
            log.error("上传视频文件到R2失败 - videoId: {}, episode: {}, 错误: {}",
                    videoId, episode.getNumber(), e.getMessage(), e);
        } catch (Exception e) {
            log.error("异步下载并上传视频文件失败 - videoId: {}, episode: {}, 错误: {}",
                    videoId, episode.getNumber(), e.getMessage(), e);
        }
    }

    /**
     * 为分集列表生成临时访问URL
     * <p>
     * 优化后的方法：直接使用DTO中的objectKey（通过@JsonIgnore隐藏），
     * 避免额外的数据库查询，提高性能
     * </p>
     *
     * @param videoEpisodes 视频分集列表
     * @param videoId 视频ID（用于缓存key）
     */
    private void generateTemporaryUrlsForEpisodes(List<VideoEpisodesDTO> videoEpisodes, Long videoId) {
        for (VideoEpisodesDTO episode : videoEpisodes) {
            try {
                // 构建缓存key
                String playUrlCacheKey = SecurityConstants.VIDEO_PLAY_URL_CACHE_PREFIX + videoId + ":" + episode.getNumber();
                String spriteUrlCacheKey = SecurityConstants.SPRITE_SHEET_URL_CACHE_PREFIX + videoId + ":" + episode.getNumber();

                // 1. 处理播放URL
                generatePlayUrl(episode, playUrlCacheKey);

                // 2. 处理雪碧图URL
                generateSpriteUrl(episode, spriteUrlCacheKey);

            } catch (Exception e) {
                log.error("生成分集临时URL失败 - episode: {}, 错误: {}", episode.getNumber(), e.getMessage(), e);
                // 如果生成临时URL失败，继续处理其他分集，但记录错误
            }
        }
    }

    /**
     * 生成播放URL
     */
    private void generatePlayUrl(VideoEpisodesDTO episode, String cacheKey) {
        // 检查缓存
        String cachedPlayUrl = redisUtil.getString(cacheKey);
        if (StringUtils.hasText(cachedPlayUrl)) {
            episode.setPlayUrl(cachedPlayUrl);
            log.debug("从缓存获取播放URL - episode: {}", episode.getNumber());
            return;
        }

        // 缓存不存在，使用DTO中的objectKey生成新的临时URL
        if (StringUtils.hasText(episode.getObjectKey())) {
            String temporaryPlayUrl = r2Service.generatePresignedGetUrl(episode.getObjectKey());
            if (StringUtils.hasText(temporaryPlayUrl)) {
                episode.setPlayUrl(temporaryPlayUrl);
                // 缓存3小时
                redisUtil.set(cacheKey, temporaryPlayUrl, SecurityConstants.TEMP_URL_CACHE_SECONDS);
                log.debug("生成并缓存播放URL - episode: {}", episode.getNumber());
            }
        }
    }

    /**
     * 生成雪碧图URL
     */
    private void generateSpriteUrl(VideoEpisodesDTO episode, String cacheKey) {
        // 检查缓存
        String cachedSpriteUrl = redisUtil.getString(cacheKey);
        if (StringUtils.hasText(cachedSpriteUrl)) {
            episode.setSpriteSheetUrl(cachedSpriteUrl);
            log.debug("从缓存获取雪碧图URL - episode: {}", episode.getNumber());
            return;
        }

        // 缓存不存在，使用DTO中的spriteSheetObjectKey生成新的临时URL
        if (StringUtils.hasText(episode.getSpriteSheetObjectKey())) {
            String temporarySpriteUrl = r2Service.generatePresignedGetUrl(episode.getSpriteSheetObjectKey());
            if (StringUtils.hasText(temporarySpriteUrl)) {
                episode.setSpriteSheetUrl(temporarySpriteUrl);
                // 缓存3小时
                redisUtil.set(cacheKey, temporarySpriteUrl, SecurityConstants.TEMP_URL_CACHE_SECONDS);
                log.debug("生成并缓存雪碧图URL - episode: {}", episode.getNumber());
            }
        }
    }

    /**
     * 从数据库获取objectKey（保留此方法以备其他地方使用）
     * <p>
     * 为了安全考虑，这个方法直接查询数据库获取objectKey
     * </p>
     *
     * @param videoId 视频ID
     * @param episodeNumber 分集编号
     * @param type 类型：video（视频文件）或 sprite（雪碧图）
     * @return objectKey，如果不存在返回null
     */
    private String getObjectKeyFromDatabase(Long videoId, String episodeNumber, String type) {
        try {
            return pureUploadEpisodesMapper.getObjectKey(videoId, episodeNumber, type);
        } catch (Exception e) {
            log.error("从数据库获取objectKey失败 - videoId: {}, episode: {}, type: {}, 错误: {}",
                    videoId, episodeNumber, type, e.getMessage(), e);
            return null;
        }
    }
}

