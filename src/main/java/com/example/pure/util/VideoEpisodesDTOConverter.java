package com.example.pure.util;

import com.example.pure.model.dto.VideoEpisodesDTO;
import com.example.pure.model.dto.VideoEpisodesPublicDTO;
import com.example.pure.model.entity.VideoEpisodes;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 视频分集DTO转换工具类
 * <p>
 * 提供不同DTO类型之间的转换方法
 * </p>
 */
public class VideoEpisodesDTOConverter {

    /**
     * 将VideoEpisodes实体转换为VideoEpisodesDTO
     * 
     * @param entity 实体对象
     * @return DTO对象
     */
    public static VideoEpisodesDTO toDTO(VideoEpisodes entity) {
        if (entity == null) {
            return null;
        }
        
        VideoEpisodesDTO dto = new VideoEpisodesDTO();
        dto.setId(entity.getId());
        dto.setVideoInfoId(entity.getVideoInfoId());
        dto.setNumber(entity.getNumber());
        dto.setObjectKey(entity.getObjectKey());
        dto.setDuration(entity.getDuration());
        dto.setSpriteSheetObjectKey(entity.getSpriteSheetObjectKey());
        dto.setCreatedTime(entity.getCreatedTime().toInstant(java.time.ZoneOffset.UTC));
        dto.setUpdatedTime(entity.getUpdatedTime().toInstant(java.time.ZoneOffset.UTC));
        
        return dto;
    }

    /**
     * 将VideoEpisodesDTO转换为公开的VideoEpisodesPublicDTO
     * <p>
     * 此方法会过滤掉敏感的objectKey字段
     * </p>
     * 
     * @param dto 内部DTO对象
     * @return 公开DTO对象
     */
    public static VideoEpisodesPublicDTO toPublicDTO(VideoEpisodesDTO dto) {
        if (dto == null) {
            return null;
        }
        
        VideoEpisodesPublicDTO publicDTO = new VideoEpisodesPublicDTO();
        publicDTO.setId(dto.getId());
        publicDTO.setVideoInfoId(dto.getVideoInfoId());
        publicDTO.setNumber(dto.getNumber());
        publicDTO.setPlayUrl(dto.getPlayUrl());
        publicDTO.setDuration(dto.getDuration());
        publicDTO.setSpriteSheetUrl(dto.getSpriteSheetUrl());
        publicDTO.setCreatedTime(dto.getCreatedTime());
        publicDTO.setUpdatedTime(dto.getUpdatedTime());
        publicDTO.setLikesCount(dto.getLikesCount());
        publicDTO.setStatus(dto.getStatus());
        
        return publicDTO;
    }

    /**
     * 批量转换VideoEpisodes实体列表为VideoEpisodesDTO列表
     * 
     * @param entities 实体列表
     * @return DTO列表
     */
    public static List<VideoEpisodesDTO> toDTOList(List<VideoEpisodes> entities) {
        if (CollectionUtils.isEmpty(entities)) {
            return Collections.emptyList();
        }
        
        return entities.stream()
                .map(VideoEpisodesDTOConverter::toDTO)
                .collect(Collectors.toList());
    }

    /**
     * 批量转换VideoEpisodesDTO列表为VideoEpisodesPublicDTO列表
     * 
     * @param dtos DTO列表
     * @return 公开DTO列表
     */
    public static List<VideoEpisodesPublicDTO> toPublicDTOList(List<VideoEpisodesDTO> dtos) {
        if (CollectionUtils.isEmpty(dtos)) {
            return Collections.emptyList();
        }
        
        return dtos.stream()
                .map(VideoEpisodesDTOConverter::toPublicDTO)
                .collect(Collectors.toList());
    }
}
