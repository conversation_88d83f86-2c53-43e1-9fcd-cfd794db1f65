<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.pure.mapper.primary.PureVideoEpisodesMapper">

    <sql id="Base_Column_List">
        id,video_info_id, number, object_key, duration, sprite_sheet_object_key, created_time, updated_time
    </sql>
    <resultMap id="VideoEpisodesResultMap" type="com.example.pure.model.dto.VideoEpisodesDTO">
        <id property="id" column="id"/>
        <result property="videoInfoId" column="video_info_id"/>
        <result property="number" column="number"/>
        <result property="objectKey" column="object_key"/>
        <result property="duration" column="duration"/>
        <result property="spriteSheetObjectKey" column="sprite_sheet_object_key"/>
        <result property="createdTime" column="created_time"/>
        <result property="updatedTime" column="updated_time"/>
        <result property="likesCount" column="likes_count"/>
        <result property="status" column="status"/>
    </resultMap>

    <!--设置查询类型为List-->
    <insert id="insertVideoEpisodes" parameterType="java.util.List"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO video_episodes(
            video_info_id, number, object_key, duration, sprite_sheet_object_key, created_time, updated_time
        )
        <!--separator每批量插入一个表加,号分开，SQL语法不加,号就会错误-->
        VALUES
        <foreach collection="videoEpisodesList" item="episode" separator=",">
            (
                #{episode.videoInfoId}, #{episode.number}, #{episode.objectKey},
                #{episode.duration}, #{episode.spriteSheetObjectKey}, NOW(), NOW()
            )
        </foreach>
        <!--
        foreach就像这样
        VALUES
    (
        'video1', 1, 'http://example.com/1', 300, NOW(), NOW()
    ),
    (
        'video1', 2, 'http://example.com/2', 320, NOW(), NOW()
    ),
    (
        'video1', 3, 'http://example.com/3', 280, NOW(), NOW()
    )
        -->
    </insert>

    <!--
      最佳实践查询:
      1. LEFT JOIN video_episodes_likes: 确保即使剧集没有点赞也能被查询出来。
      2. COUNT(CASE WHEN vel.status = true THEN vel.id END): 只统计 status 为 true 的点赞记录。
      3. GROUP BY ve.id: 按剧集ID分组，确保COUNT函数正确统计每个剧集。
      4. ORDER BY CAST(ve.number AS SIGNED) ASC: 按集数进行数字排序，避免字符串排序问题 (如 "10" 排在 "2" 前面)。
      5. 新增 LEFT JOIN vel_user: 专门用于获取当前登录用户的点赞状态。
      6. 最终优化: 合并两次JOIN为一次，使用条件聚合计算所有结果。
    -->
    <select id="findVideoEpisodesByVideoInfoId" resultMap="VideoEpisodesResultMap">
        SELECT
            ve.id,
            ve.video_info_id,
            ve.number,
            ve.object_key,
            ve.duration,
            ve.sprite_sheet_object_key,
            ve.created_time,
            ve.updated_time,

            <!-- 计算总点赞数：在分组内，计算所有status为true的记录-->
            <!--CPIMT统计查询到数据的行,CASE为SQL原生判断起始,END为判断结束,vel.status为真就返回vel.id-->
            COUNT(DISTINCT CASE WHEN vel.status = true THEN vel.id END) AS likes_count,

        <!-- 判断当前用户是否点赞：在分组内，检查是否存在当前用户且status为true的记录,JOIN + GROUP BY + 条件聚合-->
        <!-- 使用MAX函数(获取CASE表达式结果的最大值)，如果该用户点赞了(status=true)，MAX结果就是1(true)，否则是0(false)-->
        <!--IFNULL(param1,param2)如果param1不为NULL就返回，否则返回param2（为了没有人点赞形成的避免空指针）AS SIGNED把值转为整数-->
        CAST(IFNULL(MAX(CASE WHEN vel.user_id = #{userId} AND vel.status = true THEN 1 ELSE 0 END), 0) AS SIGNED) as status

    FROM
        video_episodes ve
    LEFT JOIN
        video_episodes_likes vel ON ve.id = vel.video_episode_id
    WHERE
        ve.video_info_id = #{videoInfoId}
        <!--分组,通过BY后的列为条件聚合为一行，然后通过聚合函数计算的列的值(比如SUM，把所有同id的结果行的值相加)最后id和结果的值都显示一行-->
    GROUP BY
        ve.id
    ORDER BY
        ve.number ASC
</select>

    <!-- 高性能查询：只查询基础分集信息，避免JOIN和聚合 -->
    <select id="findBasicVideoEpisodesByVideoInfoId" resultMap="VideoEpisodesResultMap">
        SELECT
            id,
            video_info_id,
            number,
            object_key,
            duration,
            sprite_sheet_object_key,
            created_time,
            updated_time
        FROM video_episodes
        WHERE video_info_id = #{videoInfoId}
        ORDER BY CAST(number AS SIGNED) ASC
    </select>

    <!-- 批量查询分集点赞数量 -->
    <resultMap id="EpisodeLikeCountResultMap" type="com.example.pure.model.dto.EpisodeLikeCountDTO">
        <result property="episodeId" column="episode_id"/>
        <result property="likesCount" column="likes_count"/>
    </resultMap>

    <select id="findLikesCountByEpisodeIds" resultMap="EpisodeLikeCountResultMap">
        SELECT
            video_episode_id as episode_id,
            COUNT(*) as likes_count
        FROM video_episodes_likes
        WHERE video_episode_id IN
              <!--用List来当查询条件语句相当于IN (1,2,3),条件为1,2,3的结果都会返回-->
        <foreach collection="episodeIds" item="episodeId" open="(" separator="," close=")">
            #{episodeId}
        </foreach>
        AND status = true
        GROUP BY video_episode_id
    </select>

    <!-- 批量查询用户点赞状态 -->
    <select id="findUserLikedEpisodeIds" resultType="Long">
        SELECT video_episode_id
        FROM video_episodes_likes
        WHERE video_episode_id IN
        <foreach collection="episodeIds" item="episodeId" open="(" separator="," close=")">
            #{episodeId}
        </foreach>
        AND user_id = #{userId}
        AND status = true
    </select>


<delete id="deleteVideoEpisodesByVideoInfoId">
    DELETE FROM video_episodes WHERE video_info_id = #{videoInfoId}
</delete>

<!-- 批量删除指定ID的分集 -->
<delete id="deleteVideoEpisodesByIds">
    DELETE FROM video_episodes
    WHERE id IN
    <foreach collection="episodeIds" item="episodeId" open="(" separator="," close=")">
        #{episodeId}
    </foreach>
</delete>

<!-- 更新指定视频分集的雪碧图对象键 -->
<update id="updateSpriteSheetObjectKey">
    UPDATE video_episodes
    SET sprite_sheet_object_key = #{spriteSheetObjectKey},
        updated_time = NOW()
    WHERE video_info_id = #{videoInfoId}
      AND number = #{episodeNumber}
</update>

<!-- 更新指定视频分集的对象键 -->
<update id="updateEpisodeObjectKey">
    UPDATE video_episodes
    SET object_key = #{objectKey},
        updated_time = NOW()
    WHERE video_info_id = #{videoInfoId}
      AND number = #{episodeNumber}
</update>

<!-- 获取指定视频分集的对象键（用于内部生成临时URL） -->
<select id="getObjectKey" resultType="String">
    SELECT
        <choose>
            <when test="type == 'video'">
                object_key
            </when>
            <when test="type == 'sprite'">
                sprite_sheet_object_key
            </when>
            <otherwise>
                NULL
            </otherwise>
        </choose>
    FROM video_episodes
    WHERE video_info_id = #{videoInfoId}
      AND number = #{episodeNumber}
    LIMIT 1
</select>

</mapper>
